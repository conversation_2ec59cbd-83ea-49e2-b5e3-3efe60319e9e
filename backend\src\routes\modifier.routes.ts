import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { ModifierController } from '../controllers/modifier.controller.js';
import { authenticateToken, requireRole } from '../middlewares/auth.middleware.js';
import { validate } from '../middlewares/validation.middleware.js';
import {
  createModifierGroupSchema,
  updateModifierGroupSchema,
  getModifierGroupSchema,
  getModifierGroupsSchema,
  deleteModifierGroupSchema,
  createModifierSchema,
  updateModifierSchema,
  getModifierSchema,
  getModifiersSchema,
  deleteModifierSchema
} from '../validators/modifier.validator.js';

export const createModifierRoutes = (prisma: PrismaClient): Router => {
  const router = Router();
  const modifierController = new ModifierController(prisma);

  // Tüm modifier endpoint'leri authentication gerektirir
  router.use(authenticateToken);

  // Modifiyer yönetimi için gerekli roller
  const modifierManagementRoles = ['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'];
  
  // Modifiyer görüntüleme için gerekli roller (daha geniş erişim)
  const modifierViewRoles = ['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER', 'CASHIER', 'WAITER'];

  // ==================== MODIFIER GROUP ENDPOINTS ====================

  // GET /api/modifiers/groups - Tüm modifiyer gruplarını listele
  router.get(
    '/groups',
    requireRole(modifierViewRoles),
    validate(getModifierGroupsSchema),
    modifierController.getModifierGroups
  );

  // GET /api/modifiers/groups/:id - Belirli bir modifiyer grubunu getir
  router.get(
    '/groups/:id',
    requireRole(modifierViewRoles),
    validate(getModifierGroupSchema),
    modifierController.getModifierGroupById
  );

  // POST /api/modifiers/groups - Yeni modifiyer grubu ekle
  router.post(
    '/groups',
    requireRole(modifierManagementRoles),
    validate(createModifierGroupSchema),
    modifierController.createModifierGroup
  );

  // PUT /api/modifiers/groups/:id - Mevcut modifiyer grubunu güncelle
  router.put(
    '/groups/:id',
    requireRole(modifierManagementRoles),
    validate(updateModifierGroupSchema),
    modifierController.updateModifierGroup
  );

  // DELETE /api/modifiers/groups/:id - Modifiyer grubunu sil
  router.delete(
    '/groups/:id',
    requireRole(modifierManagementRoles),
    validate(deleteModifierGroupSchema),
    modifierController.deleteModifierGroup
  );

  // ==================== MODIFIER ENDPOINTS ====================

  // GET /api/modifiers - Tüm modifiyerleri listele
  router.get(
    '/',
    requireRole(modifierViewRoles),
    validate(getModifiersSchema),
    modifierController.getModifiers
  );

  // GET /api/modifiers/:id - Belirli bir modifiyeri getir
  router.get(
    '/:id',
    requireRole(modifierViewRoles),
    validate(getModifierSchema),
    modifierController.getModifierById
  );

  // POST /api/modifiers - Yeni modifiyer ekle
  router.post(
    '/',
    requireRole(modifierManagementRoles),
    validate(createModifierSchema),
    modifierController.createModifier
  );

  // PUT /api/modifiers/:id - Mevcut modifiyeri güncelle
  router.put(
    '/:id',
    requireRole(modifierManagementRoles),
    validate(updateModifierSchema),
    modifierController.updateModifier
  );

  // DELETE /api/modifiers/:id - Modifiyeri sil
  router.delete(
    '/:id',
    requireRole(modifierManagementRoles),
    validate(deleteModifierSchema),
    modifierController.deleteModifier
  );

  return router;
};
