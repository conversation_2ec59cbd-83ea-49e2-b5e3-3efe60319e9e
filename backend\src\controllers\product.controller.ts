import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { ProductService } from '../services/product.service.js';
import { AuthRequest } from '../middlewares/auth.middleware.js';
import { asyncHandler } from '../middlewares/error.middleware.js';
import type { 
  CreateProductInput, 
  UpdateProductInput, 
  GetProductsQuery 
} from '../validators/product.validator.js';

export class ProductController {
  private productService: ProductService;

  constructor(prisma: PrismaClient) {
    this.productService = new ProductService(prisma);
  }

  // ==================== CREATE PRODUCT ====================
  createProduct = asyncHandler(async (req: AuthRequest, res: Response) => {
    const data: CreateProductInput = req.body;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.productService.createProduct(data, companyId);
    
    res.status(201).json(result);
  });

  // ==================== GET PRODUCTS ====================
  getProducts = asyncHandler(async (req: AuthRequest, res: Response) => {
    const query: GetProductsQuery = req.query as any;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.productService.getProducts(query, companyId);
    
    res.json(result);
  });

  // ==================== GET PRODUCT BY ID ====================
  getProductById = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.productService.getProductById(id, companyId);
    
    res.json(result);
  });

  // ==================== UPDATE PRODUCT ====================
  updateProduct = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const data: UpdateProductInput = req.body;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.productService.updateProduct(id, data, companyId);
    
    res.json(result);
  });

  // ==================== DELETE PRODUCT ====================
  deleteProduct = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.productService.deleteProduct(id, companyId);
    
    res.json(result);
  });
}
