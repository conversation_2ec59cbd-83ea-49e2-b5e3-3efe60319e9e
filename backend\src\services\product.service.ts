import { PrismaClient } from '@prisma/client';
import { createError } from '../middlewares/error.middleware.js';
import type { 
  CreateProductInput, 
  UpdateProductInput, 
  GetProductsQuery 
} from '../validators/product.validator.js';

export class ProductService {
  constructor(private prisma: PrismaClient) {}

  // ==================== CREATE PRODUCT ====================
  async createProduct(data: CreateProductInput, companyId: string) {
    // Business Rule: Ürün adı benzersiz olmalıdır
    const existingProduct = await this.prisma.product.findFirst({
      where: {
        companyId,
        name: data.name,
        active: true
      }
    });

    if (existingProduct) {
      throw createError('Bu isimde bir ürün zaten mevcut', 400);
    }

    // Business Rule: Ürün kodu benzersiz olmalıdır
    const existingCode = await this.prisma.product.findFirst({
      where: {
        companyId,
        code: data.code,
        active: true
      }
    });

    if (existingCode) {
      throw createError('Bu ürün kodu zaten kullanılıyor', 400);
    }

    // Business Rule: Kategori mevcut olmalıdır
    const category = await this.prisma.category.findFirst({
      where: {
        id: data.categoryId,
        companyId,
        active: true
      }
    });

    if (!category) {
      throw createError('Belirtilen kategori bulunamadı', 404);
    }

    // Business Rule: Vergi mevcut olmalıdır
    const tax = await this.prisma.tax.findFirst({
      where: {
        id: data.taxId,
        companyId,
        active: true
      }
    });

    if (!tax) {
      throw createError('Belirtilen vergi oranı bulunamadı', 404);
    }

    // Ürün oluştur
    const product = await this.prisma.product.create({
      data: {
        ...data,
        companyId,
        basePrice: data.basePrice,
        hasVariants: false,
        hasModifiers: false
      },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        },
        tax: {
          select: {
            id: true,
            name: true,
            rate: true
          }
        }
      }
    });

    return {
      success: true,
      data: product
    };
  }

  // ==================== GET PRODUCTS ====================
  async getProducts(query: GetProductsQuery, companyId: string) {
    const { page, limit, categoryId, search, available, sellable, trackStock } = query;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      companyId,
      active: true
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { barcode: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (available !== undefined) {
      where.available = available;
    }

    if (sellable !== undefined) {
      where.sellable = sellable;
    }

    if (trackStock !== undefined) {
      where.trackStock = trackStock;
    }

    // Get products with pagination
    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true
            }
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true
            }
          }
        },
        orderBy: [
          { displayOrder: 'asc' },
          { name: 'asc' }
        ],
        skip,
        take: limit
      }),
      this.prisma.product.count({ where })
    ]);

    return {
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  // ==================== GET PRODUCT BY ID ====================
  async getProductById(id: string, companyId: string) {
    const product = await this.prisma.product.findFirst({
      where: {
        id,
        companyId,
        active: true
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        },
        tax: {
          select: {
            id: true,
            name: true,
            rate: true
          }
        },
        modifierGroups: {
          include: {
            modifierGroup: {
              include: {
                modifiers: {
                  where: { active: true },
                  orderBy: { displayOrder: 'asc' }
                }
              }
            }
          }
        }
      }
    });

    if (!product) {
      throw createError('Ürün bulunamadı', 404);
    }

    return {
      success: true,
      data: product
    };
  }

  // ==================== UPDATE PRODUCT ====================
  async updateProduct(id: string, data: UpdateProductInput, companyId: string) {
    // Ürün mevcut mu kontrol et
    const existingProduct = await this.prisma.product.findFirst({
      where: {
        id,
        companyId,
        active: true
      }
    });

    if (!existingProduct) {
      throw createError('Ürün bulunamadı', 404);
    }

    // Business Rule: Ürün adı benzersiz olmalıdır (kendisi hariç)
    if (data.name) {
      const duplicateName = await this.prisma.product.findFirst({
        where: {
          companyId,
          name: data.name,
          active: true,
          NOT: { id }
        }
      });

      if (duplicateName) {
        throw createError('Bu isimde bir ürün zaten mevcut', 400);
      }
    }

    // Business Rule: Ürün kodu benzersiz olmalıdır (kendisi hariç)
    if (data.code) {
      const duplicateCode = await this.prisma.product.findFirst({
        where: {
          companyId,
          code: data.code,
          active: true,
          NOT: { id }
        }
      });

      if (duplicateCode) {
        throw createError('Bu ürün kodu zaten kullanılıyor', 400);
      }
    }

    // Business Rule: Kategori mevcut olmalıdır
    if (data.categoryId) {
      const category = await this.prisma.category.findFirst({
        where: {
          id: data.categoryId,
          companyId,
          active: true
        }
      });

      if (!category) {
        throw createError('Belirtilen kategori bulunamadı', 404);
      }
    }

    // Business Rule: Vergi mevcut olmalıdır
    if (data.taxId) {
      const tax = await this.prisma.tax.findFirst({
        where: {
          id: data.taxId,
          companyId,
          active: true
        }
      });

      if (!tax) {
        throw createError('Belirtilen vergi oranı bulunamadı', 404);
      }
    }

    // Ürünü güncelle
    const updatedProduct = await this.prisma.product.update({
      where: { id },
      data: {
        ...data,
        basePrice: data.basePrice ? data.basePrice : undefined
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        tax: {
          select: {
            id: true,
            name: true,
            rate: true
          }
        }
      }
    });

    return {
      success: true,
      data: updatedProduct
    };
  }

  // ==================== DELETE PRODUCT ====================
  async deleteProduct(id: string, companyId: string) {
    // Ürün mevcut mu kontrol et
    const existingProduct = await this.prisma.product.findFirst({
      where: {
        id,
        companyId,
        active: true
      }
    });

    if (!existingProduct) {
      throw createError('Ürün bulunamadı', 404);
    }

    // Business Rule: Aktif siparişlerde kullanılan ürün silinemez
    const activeOrderItems = await this.prisma.orderItem.findFirst({
      where: {
        productId: id,
        order: {
          status: {
            in: ['PENDING', 'PREPARING', 'READY']
          }
        }
      }
    });

    if (activeOrderItems) {
      throw createError('Aktif siparişlerde kullanılan ürün silinemez', 400);
    }

    // Soft delete (active = false)
    await this.prisma.product.update({
      where: { id },
      data: { active: false }
    });

    return {
      success: true,
      data: 'Ürün başarıyla silindi'
    };
  }
}
