import { z } from 'zod';

// ==================== MODIFIER GROUP SCHEMAS ====================

export const createModifierGroupSchema = z.object({
  body: z.object({
    name: z.string()
      .min(1, 'Modifiyer grup adı zorunludur')
      .max(100, 'Modifiyer grup adı en fazla 100 karakter olabilir'),
    
    minSelection: z.number()
      .min(0, 'Minimum seçim negatif olamaz')
      .default(0),
    
    maxSelection: z.number()
      .min(1, 'Maksimum seçim en az 1 olmalıdır')
      .default(1),
    
    required: z.boolean()
      .default(false),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .default(0)
  }).refine(data => data.minSelection <= data.maxSelection, {
    message: 'Minimum seçim, maksimum seçimden büyük olamaz',
    path: ['minSelection']
  })
});

export const updateModifierGroupSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Modifiyer grup ID zorunludur')
  }),
  body: z.object({
    name: z.string()
      .min(1, 'Modifiyer grup adı zorunludur')
      .max(100, 'Modifiyer grup adı en fazla 100 karakter olabilir')
      .optional(),
    
    minSelection: z.number()
      .min(0, 'Minimum seçim negatif olamaz')
      .optional(),
    
    maxSelection: z.number()
      .min(1, 'Maksimum seçim en az 1 olmalıdır')
      .optional(),
    
    required: z.boolean()
      .optional(),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .optional(),
    
    active: z.boolean()
      .optional()
  }).refine(data => {
    if (data.minSelection !== undefined && data.maxSelection !== undefined) {
      return data.minSelection <= data.maxSelection;
    }
    return true;
  }, {
    message: 'Minimum seçim, maksimum seçimden büyük olamaz',
    path: ['minSelection']
  })
});

// ==================== MODIFIER SCHEMAS ====================

export const createModifierSchema = z.object({
  body: z.object({
    groupId: z.string()
      .min(1, 'Modifiyer grup ID zorunludur'),
    
    name: z.string()
      .min(1, 'Modifiyer adı zorunludur')
      .max(100, 'Modifiyer adı en fazla 100 karakter olabilir'),
    
    price: z.number()
      .min(0, 'Modifiyer fiyatı negatif olamaz')
      .max(999999.99, 'Fiyat çok yüksek')
      .default(0),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .default(0)
  })
});

export const updateModifierSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Modifiyer ID zorunludur')
  }),
  body: z.object({
    groupId: z.string()
      .min(1, 'Modifiyer grup ID zorunludur')
      .optional(),
    
    name: z.string()
      .min(1, 'Modifiyer adı zorunludur')
      .max(100, 'Modifiyer adı en fazla 100 karakter olabilir')
      .optional(),
    
    price: z.number()
      .min(0, 'Modifiyer fiyatı negatif olamaz')
      .max(999999.99, 'Fiyat çok yüksek')
      .optional(),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .optional(),
    
    active: z.boolean()
      .optional()
  })
});

// ==================== COMMON SCHEMAS ====================

export const getModifierGroupSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Modifiyer grup ID zorunludur')
  })
});

export const getModifierSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Modifiyer ID zorunludur')
  })
});

export const getModifierGroupsSchema = z.object({
  query: z.object({
    page: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0, 'Sayfa numarası pozitif olmalıdır')
      .default('1'),
    
    limit: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0 && val <= 100, 'Limit 1-100 arasında olmalıdır')
      .default('20'),
    
    search: z.string()
      .max(100, 'Arama terimi en fazla 100 karakter olabilir')
      .optional(),
    
    active: z.string()
      .transform(val => val === 'true')
      .optional(),
    
    includeModifiers: z.string()
      .transform(val => val === 'true')
      .default('false')
  })
});

export const getModifiersSchema = z.object({
  query: z.object({
    page: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0, 'Sayfa numarası pozitif olmalıdır')
      .default('1'),
    
    limit: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0 && val <= 100, 'Limit 1-100 arasında olmalıdır')
      .default('20'),
    
    groupId: z.string()
      .optional(),
    
    search: z.string()
      .max(100, 'Arama terimi en fazla 100 karakter olabilir')
      .optional(),
    
    active: z.string()
      .transform(val => val === 'true')
      .optional()
  })
});

export const deleteModifierGroupSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Modifiyer grup ID zorunludur')
  })
});

export const deleteModifierSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Modifiyer ID zorunludur')
  })
});

// ==================== TYPE EXPORTS ====================

export type CreateModifierGroupInput = z.infer<typeof createModifierGroupSchema>['body'];
export type UpdateModifierGroupInput = z.infer<typeof updateModifierGroupSchema>['body'];
export type CreateModifierInput = z.infer<typeof createModifierSchema>['body'];
export type UpdateModifierInput = z.infer<typeof updateModifierSchema>['body'];
export type GetModifierGroupsQuery = z.infer<typeof getModifierGroupsSchema>['query'];
export type GetModifiersQuery = z.infer<typeof getModifiersSchema>['query'];
