// dashboard.types.ts
// Dashboard sayfası için TypeScript type tanımları

export interface DashboardSummary {
  totalEarning: number;
  inProgressOrders: number;
  waitingList: number;
  totalEarningChange: string;
  inProgressOrdersChange: string;
  waitingListChange: string;
}

export interface PopularDish {
  id: string;
  name: string;
  orders: number;
  image: string;
}

export interface OutOfStockItem {
  id: string;
  name: string;
  availableAt: string;
  image: string;
}

export interface DashboardOrder {
  id: string;
  tableNumber: string;
  items: number;
  customerName: string;
  status?: string; // "Ready to serve" gibi durumlar için
}

export interface DashboardResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// UI State types
export type OrderStatus = 'in_progress' | 'waiting_for_payment';

export interface DashboardFilters {
  orderStatus: OrderStatus;
  searchQuery: string;
}

// Component Props types
export interface SummaryCardProps {
  title: string;
  value: number | string;
  change: string;
  icon?: React.ReactNode;
  isLoading?: boolean;
}

export interface OrderListProps {
  orders: DashboardOrder[];
  status: OrderStatus;
  isLoading?: boolean;
  onPayNow?: (orderId: string) => void;
  onViewOrder?: (orderId: string) => void;
}

export interface PopularDishListProps {
  dishes: PopularDish[];
  isLoading?: boolean;
}

export interface OutOfStockListProps {
  items: OutOfStockItem[];
  isLoading?: boolean;
}

// API Query types
export interface OrdersQueryParams {
  status: OrderStatus;
  limit?: number;
  offset?: number;
  search?: string;
}

// Error types
export interface DashboardError {
  message: string;
  code?: string;
  details?: any;
}

// Loading states
export interface DashboardLoadingState {
  summary: boolean;
  popularDishes: boolean;
  outOfStock: boolean;
  orders: boolean;
}

// Dashboard store state
export interface DashboardState {
  summary: DashboardSummary | null;
  popularDishes: PopularDish[];
  outOfStockItems: OutOfStockItem[];
  orders: DashboardOrder[];
  filters: DashboardFilters;
  loading: DashboardLoadingState;
  error: DashboardError | null;
  
  // Actions
  setSummary: (summary: DashboardSummary) => void;
  setPopularDishes: (dishes: PopularDish[]) => void;
  setOutOfStockItems: (items: OutOfStockItem[]) => void;
  setOrders: (orders: DashboardOrder[]) => void;
  setFilters: (filters: Partial<DashboardFilters>) => void;
  setLoading: (key: keyof DashboardLoadingState, value: boolean) => void;
  setError: (error: DashboardError | null) => void;
  clearError: () => void;
}
