// dashboard.controller.ts
// Dashboard API endpoint'le<PERSON> yöneten controller

import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { DashboardService } from '../services/dashboard.service.js';
import { AuthRequest } from '../middlewares/auth.middleware.js';
import { createError } from '../middlewares/error.middleware.js';
import { DashboardResponse } from '../types/dashboard.types.js';

export class DashboardController {
  private dashboardService: DashboardService;

  constructor(prisma: PrismaClient) {
    this.dashboardService = new DashboardService(prisma);
  }

  // GET /api/dashboard/summary
  getSummary = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const branchId = req.user?.branchId;
      
      const summary = await this.dashboardService.getSummary(branchId);
      
      const response: DashboardResponse<typeof summary> = {
        success: true,
        data: summary
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };

  // GET /api/dashboard/popular-dishes
  getPopularDishes = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const branchId = req.user?.branchId;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      
      if (limit > 50) {
        throw createError('Limit cannot exceed 50', 400);
      }

      const popularDishes = await this.dashboardService.getPopularDishes(branchId, limit);
      
      const response: DashboardResponse<typeof popularDishes> = {
        success: true,
        data: popularDishes
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };

  // GET /api/dashboard/out-of-stock
  getOutOfStock = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const branchId = req.user?.branchId;
      
      const outOfStockItems = await this.dashboardService.getOutOfStockItems(branchId);
      
      const response: DashboardResponse<typeof outOfStockItems> = {
        success: true,
        data: outOfStockItems
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };

  // GET /api/dashboard/orders
  getOrders = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const branchId = req.user?.branchId;
      const status = req.query.status as 'in_progress' | 'waiting_for_payment';
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
      const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;

      // Status validation
      if (!status || !['in_progress', 'waiting_for_payment'].includes(status)) {
        throw createError('Valid status parameter is required (in_progress or waiting_for_payment)', 400);
      }

      // Limit validation
      if (limit > 100) {
        throw createError('Limit cannot exceed 100', 400);
      }

      // Offset validation
      if (offset < 0) {
        throw createError('Offset cannot be negative', 400);
      }

      const orders = await this.dashboardService.getOrders(status, branchId, limit, offset);
      
      const response: DashboardResponse<typeof orders> = {
        success: true,
        data: orders
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };

  // GET /api/dashboard/health (Dashboard sisteminin sağlık kontrolü)
  getHealth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const response = {
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          service: 'dashboard-api'
        }
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };
}
