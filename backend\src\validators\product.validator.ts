import { z } from 'zod';

// ==================== PRODUCT SCHEMAS ====================

export const createProductSchema = z.object({
  body: z.object({
    name: z.string()
      .min(1, '<PERSON>rün adı zorunludur')
      .max(100, 'Ürün adı en fazla 100 karakter olabilir'),
    
    description: z.string()
      .max(500, 'Açıklama en fazla 500 karakter olabilir')
      .optional(),
    
    basePrice: z.number()
      .positive('Ürün fiyatı pozitif olmalıdır')
      .max(999999.99, 'Fiyat çok yüksek'),
    
    categoryId: z.string()
      .min(1, 'Kategori ID zorunludur'),
    
    taxId: z.string()
      .min(1, 'Vergi ID zorunludur'),
    
    code: z.string()
      .min(1, 'Ürün kodu zorunludur')
      .max(50, '<PERSON>r<PERSON>n kodu en fazla 50 karakter olabilir'),
    
    barcode: z.string()
      .max(50, 'Barkod en fazla 50 karakter olabilir')
      .optional(),
    
    image: z.string()
      .url('Geçerli bir URL giriniz')
      .optional(),
    
    unit: z.enum(['PIECE', 'KG', 'GRAM', 'LITER', 'ML', 'PORTION'])
      .default('PIECE'),
    
    trackStock: z.boolean()
      .default(false),
    
    criticalStock: z.number()
      .min(0, 'Kritik stok negatif olamaz')
      .optional(),
    
    available: z.boolean()
      .default(true),
    
    sellable: z.boolean()
      .default(true),
    
    preparationTime: z.number()
      .min(0, 'Hazırlık süresi negatif olamaz')
      .max(1440, 'Hazırlık süresi en fazla 24 saat olabilir')
      .optional(),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .default(0)
  })
});

export const updateProductSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Ürün ID zorunludur')
  }),
  body: z.object({
    name: z.string()
      .min(1, 'Ürün adı zorunludur')
      .max(100, 'Ürün adı en fazla 100 karakter olabilir')
      .optional(),
    
    description: z.string()
      .max(500, 'Açıklama en fazla 500 karakter olabilir')
      .optional(),
    
    basePrice: z.number()
      .positive('Ürün fiyatı pozitif olmalıdır')
      .max(999999.99, 'Fiyat çok yüksek')
      .optional(),
    
    categoryId: z.string()
      .min(1, 'Kategori ID zorunludur')
      .optional(),
    
    taxId: z.string()
      .min(1, 'Vergi ID zorunludur')
      .optional(),
    
    code: z.string()
      .min(1, 'Ürün kodu zorunludur')
      .max(50, 'Ürün kodu en fazla 50 karakter olabilir')
      .optional(),
    
    barcode: z.string()
      .max(50, 'Barkod en fazla 50 karakter olabilir')
      .optional(),
    
    image: z.string()
      .url('Geçerli bir URL giriniz')
      .optional(),
    
    unit: z.enum(['PIECE', 'KG', 'GRAM', 'LITER', 'ML', 'PORTION'])
      .optional(),
    
    trackStock: z.boolean()
      .optional(),
    
    criticalStock: z.number()
      .min(0, 'Kritik stok negatif olamaz')
      .optional(),
    
    available: z.boolean()
      .optional(),
    
    sellable: z.boolean()
      .optional(),
    
    preparationTime: z.number()
      .min(0, 'Hazırlık süresi negatif olamaz')
      .max(1440, 'Hazırlık süresi en fazla 24 saat olabilir')
      .optional(),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .optional(),
    
    active: z.boolean()
      .optional()
  })
});

export const getProductSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Ürün ID zorunludur')
  })
});

export const getProductsSchema = z.object({
  query: z.object({
    page: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0, 'Sayfa numarası pozitif olmalıdır')
      .default('1'),
    
    limit: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0 && val <= 100, 'Limit 1-100 arasında olmalıdır')
      .default('20'),
    
    categoryId: z.string()
      .optional(),
    
    search: z.string()
      .max(100, 'Arama terimi en fazla 100 karakter olabilir')
      .optional(),
    
    available: z.string()
      .transform(val => val === 'true')
      .optional(),
    
    sellable: z.string()
      .transform(val => val === 'true')
      .optional(),
    
    trackStock: z.string()
      .transform(val => val === 'true')
      .optional()
  })
});

export const deleteProductSchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Ürün ID zorunludur')
  })
});

// ==================== TYPE EXPORTS ====================

export type CreateProductInput = z.infer<typeof createProductSchema>['body'];
export type UpdateProductInput = z.infer<typeof updateProductSchema>['body'];
export type GetProductsQuery = z.infer<typeof getProductsSchema>['query'];
