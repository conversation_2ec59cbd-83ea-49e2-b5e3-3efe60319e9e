import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import * as Toast from '@radix-ui/react-toast';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { apiService } from '../services/api.service';
import { useAuth } from '../hooks/useAuth';
import { UserSelect } from '../components/features/UserSelect';
import { PinInput } from '../components/features/PinInput';
import { NumericKeypad } from '../components/features/NumericKeypad';
import { QuoteDisplay } from '../components/features/QuoteDisplay';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { Button } from '../components/common/Button';
import { cn } from '../lib/utils';

export const PinLogin: React.FC = () => {
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [pin, setPin] = useState('');
  const [toastOpen, setToastOpen] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('error');

  const { pinLogin, loading: authLoading, error: authError } = useAuth();

  // Fetch available shifts
  const {
    data: users = [],
    isLoading: usersLoading,
    error: usersError
  } = useQuery({
    queryKey: ['available-shifts'],
    queryFn: () => apiService.getAvailableShifts(),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const handleNumberClick = (number: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + number);
    }
  };

  const handleDeleteClick = () => {
    setPin(prev => prev.slice(0, -1));
  };

  const handleStartShift = async () => {
    if (!selectedUserId || pin.length !== 6) {
      showToast('Lütfen kullanıcı seçin ve 6 haneli PIN girin', 'error');
      return;
    }

    const result = await pinLogin({
      userId: selectedUserId,
      pin
    });

    if (result.success) {
      showToast('Vardiya başarıyla başlatıldı!', 'success');
      // Redirect to dashboard after success
      setTimeout(() => {
        // Here you would navigate to dashboard
        console.log('Redirecting to dashboard...');
      }, 1500);
    } else {
      showToast(result.error || 'Giriş başarısız', 'error');
      setPin(''); // Clear PIN on error
    }
  };

  const showToast = (message: string, type: 'success' | 'error') => {
    setToastMessage(message);
    setToastType(type);
    setToastOpen(true);
  };

  const isFormValid = selectedUserId && pin.length === 6;
  const isLoading = authLoading || usersLoading;

  if (usersError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Bağlantı Hatası
          </h2>
          <p className="text-gray-600 mb-4">
            Sunucuya bağlanılamıyor. Lütfen backend sunucusunun çalıştığından emin olun.
          </p>
          <Button onClick={() => window.location.reload()}>
            Tekrar Dene
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Toast.Provider swipeDirection="right">
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="container mx-auto px-4 py-8">
          <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto items-center">
            {/* Left Side - Quote Display */}
            <div className="space-y-8">
              <div className="text-center lg:text-left">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                  Atropos POS
                </h1>
                <p className="text-xl text-gray-600">
                  Vardiya Giriş Sistemi
                </p>
              </div>
              <QuoteDisplay />
            </div>

            {/* Right Side - Login Form */}
            <div className="flex justify-center">
              <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Vardiya Başlat
                  </h2>
                  <p className="text-gray-600">
                    Çalışan seçin ve PIN kodunuzu girin
                  </p>
                </div>

                {/* User Selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Çalışan Seçimi
                  </label>
                  {usersLoading ? (
                    <div className="h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                      <LoadingSpinner size="sm" />
                    </div>
                  ) : (
                    <UserSelect
                      users={users}
                      selectedUserId={selectedUserId}
                      onUserSelect={setSelectedUserId}
                      disabled={isLoading}
                    />
                  )}
                </div>

                {/* PIN Input */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-4">
                    PIN Kodu (6 Hane)
                  </label>
                  <PinInput
                    pin={pin}
                    error={!!authError}
                    className="mb-6"
                  />
                </div>

                {/* Numeric Keypad */}
                <div className="mb-6">
                  <NumericKeypad
                    onNumberClick={handleNumberClick}
                    onDeleteClick={handleDeleteClick}
                    disabled={isLoading}
                  />
                </div>

                {/* Start Shift Button */}
                <Button
                  variant="primary"
                  onClick={handleStartShift}
                  disabled={!isFormValid || isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <LoadingSpinner size="sm" />
                      <span>Giriş yapılıyor...</span>
                    </div>
                  ) : (
                    'Vardiya Başlat'
                  )}
                </Button>

                {/* Error Display */}
                {authError && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-4 h-4 text-red-500" />
                      <span className="text-sm text-red-700">{authError}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Toast Notifications */}
      <Toast.Root
        className={cn(
          'bg-white rounded-lg shadow-lg border p-4 grid grid-cols-[auto_max-content] gap-x-4 items-center',
          'data-[state=open]:animate-slideIn data-[state=closed]:animate-hide data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)]',
          'data-[swipe=cancel]:translate-x-0 data-[swipe=cancel]:transition-[transform_200ms_ease-out]',
          'data-[swipe=end]:animate-swipeOut',
          toastType === 'success' ? 'border-green-200' : 'border-red-200'
        )}
        open={toastOpen}
        onOpenChange={setToastOpen}
      >
        <div className="flex items-center space-x-2">
          {toastType === 'success' ? (
            <CheckCircle className="w-5 h-5 text-green-500" />
          ) : (
            <AlertCircle className="w-5 h-5 text-red-500" />
          )}
          <Toast.Description className={cn(
            'text-sm font-medium',
            toastType === 'success' ? 'text-green-700' : 'text-red-700'
          )}>
            {toastMessage}
          </Toast.Description>
        </div>
        <Toast.Close className="text-gray-400 hover:text-gray-600">
          ×
        </Toast.Close>
      </Toast.Root>

      <Toast.Viewport className="fixed bottom-0 right-0 flex flex-col p-6 gap-2 w-96 max-w-[100vw] m-0 list-none z-50 outline-none" />
    </Toast.Provider>
  );
};
