import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { createAuthRoutes } from './routes/auth.routes.js';
import { createUsersRoutes } from './routes/users.routes.js';
import { createDashboardRoutes } from './routes/dashboard.routes.js';
import { createProductRoutes } from './routes/product.routes.js';
import { createCategoryRoutes } from './routes/category.routes.js';
import { createModifierRoutes } from './routes/modifier.routes.js';
import { errorHandler } from './middlewares/error.middleware.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;
const prisma = new PrismaClient();

app.use(helmet());
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/auth', createAuthRoutes(prisma));
app.use('/api/users', createUsersRoutes(prisma));
app.use('/api/dashboard', createDashboardRoutes(prisma));
app.use('/api/products', createProductRoutes(prisma));
app.use('/api/categories', createCategoryRoutes(prisma));
app.use('/api/modifiers', createModifierRoutes(prisma));

// Health check
app.get('/api/health', async (req, res) => {
  try {
    // Test database connection
    await prisma.$connect();
    await prisma.$disconnect();

    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: 'Connected'
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: 'Disconnected',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Error handling middleware (must be last)
app.use(errorHandler);

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  /api/health`);
  console.log(`   GET  /api/users/available-shifts`);
  console.log(`   POST /api/auth/pin-login`);
  console.log(`   GET  /api/dashboard/summary`);
  console.log(`   GET  /api/dashboard/popular-dishes`);
  console.log(`   GET  /api/dashboard/out-of-stock`);
  console.log(`   GET  /api/dashboard/orders`);
  console.log(`   GET  /api/products`);
  console.log(`   GET  /api/categories`);
  console.log(`   GET  /api/modifiers`);
  console.log(`   GET  /api/modifiers/groups`);
});
