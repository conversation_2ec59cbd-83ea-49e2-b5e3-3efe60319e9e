import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { ModifierService } from '../services/modifier.service.js';
import { AuthRequest } from '../middlewares/auth.middleware.js';
import { asyncHandler } from '../middlewares/error.middleware.js';
import type { 
  CreateModifierGroupInput, 
  UpdateModifierGroupInput,
  CreateModifierInput,
  UpdateModifierInput,
  GetModifierGroupsQuery,
  GetModifiersQuery
} from '../validators/modifier.validator.js';

export class ModifierController {
  private modifierService: ModifierService;

  constructor(prisma: PrismaClient) {
    this.modifierService = new ModifierService(prisma);
  }

  // ==================== MODIFIER GROUP METHODS ====================

  createModifierGroup = asyncHandler(async (req: AuthRequest, res: Response) => {
    const data: CreateModifierGroupInput = req.body;

    const result = await this.modifierService.createModifierGroup(data);
    
    res.status(201).json(result);
  });

  getModifierGroups = asyncHandler(async (req: AuthRequest, res: Response) => {
    const query: GetModifierGroupsQuery = req.query as any;

    const result = await this.modifierService.getModifierGroups(query);
    
    res.json(result);
  });

  getModifierGroupById = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;

    const result = await this.modifierService.getModifierGroupById(id);
    
    res.json(result);
  });

  updateModifierGroup = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const data: UpdateModifierGroupInput = req.body;

    const result = await this.modifierService.updateModifierGroup(id, data);
    
    res.json(result);
  });

  deleteModifierGroup = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;

    const result = await this.modifierService.deleteModifierGroup(id);
    
    res.json(result);
  });

  // ==================== MODIFIER METHODS ====================

  createModifier = asyncHandler(async (req: AuthRequest, res: Response) => {
    const data: CreateModifierInput = req.body;

    const result = await this.modifierService.createModifier(data);
    
    res.status(201).json(result);
  });

  getModifiers = asyncHandler(async (req: AuthRequest, res: Response) => {
    const query: GetModifiersQuery = req.query as any;

    const result = await this.modifierService.getModifiers(query);
    
    res.json(result);
  });

  getModifierById = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;

    const result = await this.modifierService.getModifierById(id);
    
    res.json(result);
  });

  updateModifier = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const data: UpdateModifierInput = req.body;

    const result = await this.modifierService.updateModifier(id, data);
    
    res.json(result);
  });

  deleteModifier = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;

    const result = await this.modifierService.deleteModifier(id);
    
    res.json(result);
  });
}
