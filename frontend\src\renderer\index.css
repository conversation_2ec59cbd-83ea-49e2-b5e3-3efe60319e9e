@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer base {
  html, body {
    @apply bg-background text-foreground;
    margin: 0;
    padding: 0;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  /* Electron specific styles */
  body {
    -webkit-app-region: no-drag;
    user-select: none;
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Radix UI Dialog animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  @keyframes zoomIn {
    from {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  @keyframes zoomOut {
    from {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    to {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.95);
    }
  }

  .animate-in {
    animation-duration: 200ms;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 150ms;
    animation-fill-mode: both;
  }

  .fade-in-0 {
    animation-name: fadeIn;
  }

  .fade-out-0 {
    animation-name: fadeOut;
  }

  .zoom-in-95 {
    animation-name: zoomIn;
  }

  .zoom-out-95 {
    animation-name: zoomOut;
  }

  .slide-in-from-left-1\/2 {
    animation-name: zoomIn;
  }

  .slide-in-from-top-48 {
    animation-name: zoomIn;
  }

  .slide-out-to-left-1\/2 {
    animation-name: zoomOut;
  }

  .slide-out-to-top-48 {
    animation-name: zoomOut;
  }
}
