// Product types for frontend
export interface Product {
  id: string;
  companyId: string;
  categoryId: string;
  code: string;
  barcode?: string;
  name: string;
  description?: string;
  image?: string;
  basePrice: number;
  taxId: string;
  trackStock: boolean;
  unit: ProductUnit;
  criticalStock?: number;
  available: boolean;
  sellable: boolean;
  preparationTime?: number;
  hasVariants: boolean;
  hasModifiers: boolean;
  displayOrder: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  category?: Category;
  tax?: Tax;
  modifierGroups?: ProductModifierGroup[];
}

export interface Category {
  id: string;
  companyId: string;
  parentId?: string;
  name: string;
  description?: string;
  image?: string;
  color?: string;
  icon?: string;
  displayOrder: number;
  active: boolean;
  printerGroupId?: string;
  preparationTime?: number;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  parent?: Category;
  children?: Category[];
  products?: Product[];
  _count?: {
    products: number;
    children: number;
  };
}

export interface ModifierGroup {
  id: string;
  name: string;
  minSelection: number;
  maxSelection: number;
  required: boolean;
  displayOrder: number;
  active: boolean;
  
  // Relations
  modifiers?: Modifier[];
  _count?: {
    modifiers: number;
    products: number;
  };
}

export interface Modifier {
  id: string;
  groupId: string;
  name: string;
  price: number;
  displayOrder: number;
  active: boolean;
  
  // Relations
  group?: ModifierGroup;
}

export interface ProductModifierGroup {
  id: string;
  productId: string;
  groupId: string;
  required: boolean;
  displayOrder: number;
  
  // Relations
  product?: Product;
  group?: ModifierGroup;
}

export interface Tax {
  id: string;
  name: string;
  rate: number;
  active: boolean;
}

export enum ProductUnit {
  PIECE = 'PIECE',
  KG = 'KG',
  GRAM = 'GRAM',
  LITER = 'LITER',
  ML = 'ML',
  PORTION = 'PORTION'
}

// API Request/Response types
export interface CreateProductRequest {
  name: string;
  description?: string;
  basePrice: number;
  categoryId: string;
  taxId: string;
  code: string;
  barcode?: string;
  image?: string;
  unit?: ProductUnit;
  trackStock?: boolean;
  criticalStock?: number;
  available?: boolean;
  sellable?: boolean;
  preparationTime?: number;
  displayOrder?: number;
}

export interface UpdateProductRequest {
  name?: string;
  description?: string;
  basePrice?: number;
  categoryId?: string;
  taxId?: string;
  code?: string;
  barcode?: string;
  image?: string;
  unit?: ProductUnit;
  trackStock?: boolean;
  criticalStock?: number;
  available?: boolean;
  sellable?: boolean;
  preparationTime?: number;
  displayOrder?: number;
  active?: boolean;
}

export interface GetProductsQuery {
  page?: number;
  limit?: number;
  categoryId?: string;
  search?: string;
  available?: boolean;
  sellable?: boolean;
  trackStock?: boolean;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  parentId?: string;
  image?: string;
  color?: string;
  icon?: string;
  displayOrder?: number;
  printerGroupId?: string;
  preparationTime?: number;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  parentId?: string;
  image?: string;
  color?: string;
  icon?: string;
  displayOrder?: number;
  active?: boolean;
  printerGroupId?: string;
  preparationTime?: number;
}

export interface GetCategoriesQuery {
  page?: number;
  limit?: number;
  parentId?: string;
  search?: string;
  active?: boolean;
  includeProducts?: boolean;
}

export interface CreateModifierGroupRequest {
  name: string;
  minSelection?: number;
  maxSelection?: number;
  required?: boolean;
  displayOrder?: number;
}

export interface UpdateModifierGroupRequest {
  name?: string;
  minSelection?: number;
  maxSelection?: number;
  required?: boolean;
  displayOrder?: number;
  active?: boolean;
}

export interface CreateModifierRequest {
  groupId: string;
  name: string;
  price?: number;
  displayOrder?: number;
}

export interface UpdateModifierRequest {
  groupId?: string;
  name?: string;
  price?: number;
  displayOrder?: number;
  active?: boolean;
}

export interface GetModifierGroupsQuery {
  page?: number;
  limit?: number;
  search?: string;
  active?: boolean;
  includeModifiers?: boolean;
}

export interface GetModifiersQuery {
  page?: number;
  limit?: number;
  groupId?: string;
  search?: string;
  active?: boolean;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface ProductFormData {
  name: string;
  description: string;
  basePrice: string;
  categoryId: string;
  taxId: string;
  code: string;
  barcode: string;
  image: string;
  unit: ProductUnit;
  trackStock: boolean;
  criticalStock: string;
  available: boolean;
  sellable: boolean;
  preparationTime: string;
  displayOrder: string;
}

export interface CategoryFormData {
  name: string;
  description: string;
  parentId: string;
  image: string;
  color: string;
  icon: string;
  displayOrder: string;
  printerGroupId: string;
  preparationTime: string;
}

export interface ModifierGroupFormData {
  name: string;
  minSelection: string;
  maxSelection: string;
  required: boolean;
  displayOrder: string;
}

export interface ModifierFormData {
  groupId: string;
  name: string;
  price: string;
  displayOrder: string;
}
