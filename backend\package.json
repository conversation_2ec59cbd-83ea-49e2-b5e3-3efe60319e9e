{"name": "pos-backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.7.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "zod": "^4.0.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.10.2", "prisma": "^6.7.0", "tsx": "^4.19.7", "typescript": "^5.7.2"}}