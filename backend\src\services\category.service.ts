import { PrismaClient } from '@prisma/client';
import { createError } from '../middlewares/error.middleware.js';
import type { 
  CreateCategoryInput, 
  UpdateCategoryInput, 
  GetCategoriesQuery 
} from '../validators/category.validator.js';

export class CategoryService {
  constructor(private prisma: PrismaClient) {}

  // ==================== CREATE CATEGORY ====================
  async createCategory(data: CreateCategoryInput, companyId: string) {
    // Business Rule: Kategori adı benzersiz olmalıdır
    const existingCategory = await this.prisma.category.findFirst({
      where: {
        companyId,
        name: data.name,
        active: true
      }
    });

    if (existingCategory) {
      throw createError('Bu isimde bir kategori zaten mevcut', 400);
    }

    // Business Rule: Parent kategori mevcut olmalıdır (eğer belirt<PERSON>)
    if (data.parentId) {
      const parentCategory = await this.prisma.category.findFirst({
        where: {
          id: data.parentId,
          companyId,
          active: true
        }
      });

      if (!parentCategory) {
        throw createError('Belirtilen üst kategori bulunamadı', 404);
      }
    }

    // Business Rule: Printer group mevcut olmalıdır (eğer belirtilmişse)
    if (data.printerGroupId) {
      const printerGroup = await this.prisma.printerGroup.findFirst({
        where: {
          id: data.printerGroupId
        }
      });

      if (!printerGroup) {
        throw createError('Belirtilen yazıcı grubu bulunamadı', 404);
      }
    }

    // Kategori oluştur
    const category = await this.prisma.category.create({
      data: {
        ...data,
        companyId
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            products: {
              where: { active: true }
            },
            children: {
              where: { active: true }
            }
          }
        }
      }
    });

    return {
      success: true,
      data: category
    };
  }

  // ==================== GET CATEGORIES ====================
  async getCategories(query: GetCategoriesQuery, companyId: string) {
    const { page, limit, parentId, search, active, includeProducts } = query;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      companyId
    };

    if (active !== undefined) {
      where.active = active;
    } else {
      where.active = true; // Default olarak sadece aktif kategoriler
    }

    if (parentId !== undefined) {
      where.parentId = parentId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Include options
    const include: any = {
      parent: {
        select: {
          id: true,
          name: true
        }
      },
      _count: {
        select: {
          products: {
            where: { active: true }
          },
          children: {
            where: { active: true }
          }
        }
      }
    };

    if (includeProducts) {
      include.products = {
        where: { active: true },
        select: {
          id: true,
          name: true,
          basePrice: true,
          image: true,
          available: true,
          sellable: true
        },
        orderBy: [
          { displayOrder: 'asc' },
          { name: 'asc' }
        ]
      };
    }

    // Get categories with pagination
    const [categories, total] = await Promise.all([
      this.prisma.category.findMany({
        where,
        include,
        orderBy: [
          { displayOrder: 'asc' },
          { name: 'asc' }
        ],
        skip: Number(skip),
        take: Number(limit)
      }),
      this.prisma.category.count({ where })
    ]);

    return {
      success: true,
      data: categories,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  // ==================== GET CATEGORY BY ID ====================
  async getCategoryById(id: string, companyId: string) {
    const category = await this.prisma.category.findFirst({
      where: {
        id,
        companyId,
        active: true
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        children: {
          where: { active: true },
          select: {
            id: true,
            name: true,
            image: true,
            color: true,
            icon: true,
            displayOrder: true,
            _count: {
              select: {
                products: {
                  where: { active: true }
                }
              }
            }
          },
          orderBy: { displayOrder: 'asc' }
        },
        products: {
          where: { active: true },
          select: {
            id: true,
            name: true,
            basePrice: true,
            image: true,
            available: true,
            sellable: true,
            displayOrder: true
          },
          orderBy: [
            { displayOrder: 'asc' },
            { name: 'asc' }
          ]
        },
        _count: {
          select: {
            products: {
              where: { active: true }
            },
            children: {
              where: { active: true }
            }
          }
        }
      }
    });

    if (!category) {
      throw createError('Kategori bulunamadı', 404);
    }

    return {
      success: true,
      data: category
    };
  }

  // ==================== UPDATE CATEGORY ====================
  async updateCategory(id: string, data: UpdateCategoryInput, companyId: string) {
    // Kategori mevcut mu kontrol et
    const existingCategory = await this.prisma.category.findFirst({
      where: {
        id,
        companyId,
        active: true
      }
    });

    if (!existingCategory) {
      throw createError('Kategori bulunamadı', 404);
    }

    // Business Rule: Kategori adı benzersiz olmalıdır (kendisi hariç)
    if (data.name) {
      const duplicateName = await this.prisma.category.findFirst({
        where: {
          companyId,
          name: data.name,
          active: true,
          NOT: { id }
        }
      });

      if (duplicateName) {
        throw createError('Bu isimde bir kategori zaten mevcut', 400);
      }
    }

    // Business Rule: Parent kategori mevcut olmalıdır (eğer belirtilmişse)
    if (data.parentId) {
      const parentCategory = await this.prisma.category.findFirst({
        where: {
          id: data.parentId,
          companyId,
          active: true
        }
      });

      if (!parentCategory) {
        throw createError('Belirtilen üst kategori bulunamadı', 404);
      }

      // Business Rule: Kategori kendisinin alt kategorisi olamaz
      if (data.parentId === id) {
        throw createError('Kategori kendisinin alt kategorisi olamaz', 400);
      }

      // Business Rule: Döngüsel referans kontrolü
      const isCircular = await this.checkCircularReference(id, data.parentId);
      if (isCircular) {
        throw createError('Döngüsel kategori referansı oluşturulamaz', 400);
      }
    }

    // Business Rule: Printer group mevcut olmalıdır (eğer belirtilmişse)
    if (data.printerGroupId) {
      const printerGroup = await this.prisma.printerGroup.findFirst({
        where: {
          id: data.printerGroupId
        }
      });

      if (!printerGroup) {
        throw createError('Belirtilen yazıcı grubu bulunamadı', 404);
      }
    }

    // Kategoriyi güncelle
    const updatedCategory = await this.prisma.category.update({
      where: { id },
      data,
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            products: {
              where: { active: true }
            },
            children: {
              where: { active: true }
            }
          }
        }
      }
    });

    return {
      success: true,
      data: updatedCategory
    };
  }

  // ==================== DELETE CATEGORY ====================
  async deleteCategory(id: string, companyId: string) {
    // Kategori mevcut mu kontrol et
    const existingCategory = await this.prisma.category.findFirst({
      where: {
        id,
        companyId,
        active: true
      }
    });

    if (!existingCategory) {
      throw createError('Kategori bulunamadı', 404);
    }

    // Business Rule: Alt kategorileri olan kategori silinemez
    const childCategories = await this.prisma.category.findFirst({
      where: {
        parentId: id,
        active: true
      }
    });

    if (childCategories) {
      throw createError('Alt kategorileri olan kategori silinemez', 400);
    }

    // Business Rule: Ürünleri olan kategori silinemez
    const products = await this.prisma.product.findFirst({
      where: {
        categoryId: id,
        active: true
      }
    });

    if (products) {
      throw createError('Ürünleri olan kategori silinemez', 400);
    }

    // Soft delete (active = false)
    await this.prisma.category.update({
      where: { id },
      data: { active: false }
    });

    return {
      success: true,
      data: 'Kategori başarıyla silindi'
    };
  }

  // ==================== HELPER METHODS ====================
  private async checkCircularReference(categoryId: string, parentId: string): Promise<boolean> {
    let currentParentId: string | null = parentId;

    while (currentParentId) {
      if (currentParentId === categoryId) {
        return true; // Döngüsel referans bulundu
      }

      const parent: { parentId: string | null } | null = await this.prisma.category.findUnique({
        where: { id: currentParentId },
        select: { parentId: true }
      });

      currentParentId = parent?.parentId ?? null;
    }
    
    return false; // Döngüsel referans yok
  }
}
