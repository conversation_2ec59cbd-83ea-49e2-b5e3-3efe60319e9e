import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { CategoryService } from '../services/category.service.js';
import { AuthRequest } from '../middlewares/auth.middleware.js';
import { asyncHandler } from '../middlewares/error.middleware.js';
import type { 
  CreateCategoryInput, 
  UpdateCategoryInput, 
  GetCategoriesQuery 
} from '../validators/category.validator.js';

export class CategoryController {
  private categoryService: CategoryService;

  constructor(prisma: PrismaClient) {
    this.categoryService = new CategoryService(prisma);
  }

  // ==================== CREATE CATEGORY ====================
  createCategory = asyncHandler(async (req: AuthRequest, res: Response) => {
    const data: CreateCategoryInput = req.body;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.categoryService.createCategory(data, companyId);
    
    res.status(201).json(result);
  });

  // ==================== GET CATEGORIES ====================
  getCategories = asyncHandler(async (req: AuthRequest, res: Response) => {
    const query: GetCategoriesQuery = req.query as any;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.categoryService.getCategories(query, companyId);
    
    res.json(result);
  });

  // ==================== GET CATEGORY BY ID ====================
  getCategoryById = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.categoryService.getCategoryById(id, companyId);
    
    res.json(result);
  });

  // ==================== UPDATE CATEGORY ====================
  updateCategory = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const data: UpdateCategoryInput = req.body;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.categoryService.updateCategory(id, data, companyId);
    
    res.json(result);
  });

  // ==================== DELETE CATEGORY ====================
  deleteCategory = asyncHandler(async (req: AuthRequest, res: Response) => {
    const { id } = req.params;
    const companyId = req.user?.companyId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID bulunamadı'
      });
    }

    const result = await this.categoryService.deleteCategory(id, companyId);
    
    res.json(result);
  });
}
