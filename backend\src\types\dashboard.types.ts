// dashboard.types.ts
// Dashboard API için TypeScript type tanımları

export interface DashboardSummary {
  totalEarning: number;
  inProgressOrders: number;
  waitingList: number;
  totalEarningChange: string;
  inProgressOrdersChange: string;
  waitingListChange: string;
}

export interface PopularDish {
  id: string;
  name: string;
  orders: number;
  image: string;
}

export interface OutOfStockItem {
  id: string;
  name: string;
  availableAt: string;
  image: string;
}

export interface DashboardOrder {
  id: string;
  tableNumber: string;
  items: number;
  customerName: string;
  status?: string; // "Ready to serve" gibi durumlar için
}

export interface DashboardResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Query parametreleri için
export interface OrdersQueryParams {
  status?: 'in_progress' | 'waiting_for_payment';
  limit?: number;
  offset?: number;
}

// Internal types (service katmanında kullanılacak)
export interface OrderSummaryData {
  totalEarning: number;
  inProgressCount: number;
  waitingCount: number;
  previousEarning?: number;
  previousInProgress?: number;
  previousWaiting?: number;
}

export interface PopularDishData {
  productId: string;
  productName: string;
  orderCount: number;
  productImage?: string;
}

export interface OutOfStockData {
  productId: string;
  productName: string;
  estimatedAvailableTime?: Date;
  productImage?: string;
}

export interface OrderListData {
  orderId: string;
  tableNumber: string;
  itemCount: number;
  customerName?: string;
  orderStatus: string;
  orderType: string;
}
