// dashboard.service.ts
// Dashboard API için business logic service

import { PrismaClient } from '@prisma/client';
import { 
  DashboardSummary, 
  PopularDish, 
  OutOfStockItem, 
  DashboardOrder,
  OrderSummaryData,
  PopularDishData,
  OutOfStockData,
  OrderListData
} from '../types/dashboard.types.js';

export class DashboardService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async getSummary(branchId?: string): Promise<DashboardSummary> {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Bugünkü veriler
    const todayData = await this.getOrderSummaryForDate(today, branchId);
    
    // Dünkü veriler (karşılaştırma için)
    const yesterdayData = await this.getOrderSummaryForDate(yesterday, branchId);

    return {
      totalEarning: todayData.totalEarning,
      inProgressOrders: todayData.inProgressCount,
      waitingList: todayData.waitingCount,
      totalEarningChange: this.calculatePercentageChange(
        yesterdayData.totalEarning, 
        todayData.totalEarning
      ),
      inProgressOrdersChange: this.calculatePercentageChange(
        yesterdayData.inProgressCount, 
        todayData.inProgressCount
      ),
      waitingListChange: this.calculatePercentageChange(
        yesterdayData.waitingCount, 
        todayData.waitingCount
      )
    };
  }

  async getPopularDishes(branchId?: string, limit: number = 10): Promise<PopularDish[]> {
    const whereClause: any = {
      orderedAt: {
        gte: new Date(new Date().setDate(new Date().getDate() - 7)) // Son 7 gün
      }
    };

    if (branchId) {
      whereClause.branchId = branchId;
    }

    const popularDishes = await this.prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        order: whereClause
      },
      _count: {
        productId: true
      },
      orderBy: {
        _count: {
          productId: 'desc'
        }
      },
      take: limit
    });

    // Ürün detaylarını al
    const dishesWithDetails = await Promise.all(
      popularDishes.map(async (dish) => {
        const product = await this.prisma.product.findUnique({
          where: { id: dish.productId },
          select: {
            id: true,
            name: true,
            image: true
          }
        });

        return {
          id: product?.id || dish.productId,
          name: product?.name || 'Bilinmeyen Ürün',
          orders: dish._count.productId,
          image: product?.image || '/images/default-dish.jpg'
        };
      })
    );

    return dishesWithDetails;
  }

  async getOutOfStockItems(branchId?: string): Promise<OutOfStockItem[]> {
    const whereClause: any = {
      available: false,
      active: true
    };

    if (branchId) {
      whereClause.companyId = branchId; // Şirket bazında filtreleme
    }

    const outOfStockProducts = await this.prisma.product.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        image: true
      },
      take: 20
    });

    return outOfStockProducts.map(product => ({
      id: product.id,
      name: product.name,
      availableAt: this.generateEstimatedTime(), // Gerçek sistemde stok takibinden gelecek
      image: product.image || '/images/default-dish.jpg'
    }));
  }

  async getOrders(
    status: 'in_progress' | 'waiting_for_payment', 
    branchId?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<DashboardOrder[]> {
    let orderStatuses: string[] = [];
    
    if (status === 'in_progress') {
      orderStatuses = ['CONFIRMED', 'PREPARING', 'READY'];
    } else if (status === 'waiting_for_payment') {
      orderStatuses = ['COMPLETED'];
    }

    const whereClause: any = {
      status: {
        in: orderStatuses
      },
      paymentStatus: status === 'waiting_for_payment' ? 'UNPAID' : undefined
    };

    if (branchId) {
      whereClause.branchId = branchId;
    }

    const orders = await this.prisma.order.findMany({
      where: whereClause,
      include: {
        table: true,
        items: true
      },
      orderBy: {
        orderedAt: 'desc'
      },
      take: limit,
      skip: offset
    });

    return orders.map(order => {
      const baseOrder: DashboardOrder = {
        id: order.id,
        tableNumber: order.table?.number || 'Paket',
        items: order.items.length,
        customerName: order.customerName || 'Misafir'
      };

      // In progress siparişler için status ekle
      if (status === 'in_progress') {
        baseOrder.status = this.mapOrderStatusToDisplay(order.status);
      }

      return baseOrder;
    });
  }

  private async getOrderSummaryForDate(date: Date, branchId?: string): Promise<OrderSummaryData> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const whereClause: any = {
      orderedAt: {
        gte: startOfDay,
        lte: endOfDay
      }
    };

    if (branchId) {
      whereClause.branchId = branchId;
    }

    const orders = await this.prisma.order.findMany({
      where: whereClause,
      select: {
        totalAmount: true,
        status: true,
        paymentStatus: true
      }
    });

    const totalEarning = orders
      .filter(order => order.paymentStatus === 'PAID')
      .reduce((sum, order) => sum + Number(order.totalAmount), 0);

    const inProgressCount = orders.filter(order => 
      ['CONFIRMED', 'PREPARING', 'READY'].includes(order.status)
    ).length;

    const waitingCount = orders.filter(order => 
      order.status === 'COMPLETED' && order.paymentStatus === 'UNPAID'
    ).length;

    return {
      totalEarning,
      inProgressCount,
      waitingCount
    };
  }

  private calculatePercentageChange(oldValue: number, newValue: number): string {
    if (oldValue === 0) {
      return newValue > 0 ? '+100%' : '0%';
    }

    const change = ((newValue - oldValue) / oldValue) * 100;
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(1)}%`;
  }

  private mapOrderStatusToDisplay(status: string): string {
    const statusMap: Record<string, string> = {
      'CONFIRMED': 'Onaylandı',
      'PREPARING': 'Hazırlanıyor',
      'READY': 'Servis için hazır',
      'DELIVERING': 'Teslimatta'
    };

    return statusMap[status] || status;
  }

  private generateEstimatedTime(): string {
    // Gerçek sistemde stok takibi ve tedarik sürelerinden hesaplanacak
    // Şimdilik örnek saat döndürüyoruz
    const now = new Date();
    const estimatedTime = new Date(now.getTime() + (2 * 60 * 60 * 1000)); // 2 saat sonra
    
    return estimatedTime.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }
}
