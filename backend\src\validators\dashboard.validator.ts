// dashboard.validator.ts
// Dashboard API için Zod validation şemaları

import { z } from 'zod';

// Query parametreleri için validator
export const ordersQuerySchema = z.object({
  status: z.enum(['in_progress', 'waiting_for_payment']).optional(),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional(),
  offset: z.string().transform(Number).pipe(z.number().min(0)).optional()
});

// Response validation şemaları
export const dashboardSummarySchema = z.object({
  totalEarning: z.number(),
  inProgressOrders: z.number(),
  waitingList: z.number(),
  totalEarningChange: z.string(),
  inProgressOrdersChange: z.string(),
  waitingListChange: z.string()
});

export const popularDishSchema = z.object({
  id: z.string(),
  name: z.string(),
  orders: z.number(),
  image: z.string()
});

export const outOfStockItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  availableAt: z.string(),
  image: z.string()
});

export const dashboardOrderSchema = z.object({
  id: z.string(),
  tableNumber: z.string(),
  items: z.number(),
  customerName: z.string(),
  status: z.string().optional()
});

// Array validation şemaları
export const popularDishesSchema = z.array(popularDishSchema);
export const outOfStockItemsSchema = z.array(outOfStockItemSchema);
export const dashboardOrdersSchema = z.array(dashboardOrderSchema);

// Response wrapper şeması
export const dashboardResponseSchema = <T>(dataSchema: z.ZodSchema<T>) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: z.string().optional()
  });

// Type exports
export type OrdersQueryParams = z.infer<typeof ordersQuerySchema>;
export type DashboardSummary = z.infer<typeof dashboardSummarySchema>;
export type PopularDish = z.infer<typeof popularDishSchema>;
export type OutOfStockItem = z.infer<typeof outOfStockItemSchema>;
export type DashboardOrder = z.infer<typeof dashboardOrderSchema>;
