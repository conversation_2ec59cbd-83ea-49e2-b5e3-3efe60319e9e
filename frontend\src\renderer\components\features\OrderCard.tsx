// OrderCard.tsx
// Sipariş kartları için component

import React from 'react';
import { Card, CardContent } from '../common/Card';
import { Button } from '../common/Button';
import { DashboardOrder, OrderStatus } from '../../types/dashboard.types';
import { cn } from '../../lib/utils';
import { User, Package, Clock, CreditCard } from 'lucide-react';

export interface OrderCardProps {
  order: DashboardOrder;
  status: OrderStatus;
  onPayNow?: (orderId: string) => void;
  onViewOrder?: (orderId: string) => void;
  className?: string;
}

export const OrderCard: React.FC<OrderCardProps> = ({
  order,
  status,
  onPayNow,
  onViewOrder,
  className
}) => {
  const getStatusColor = (orderStatus?: string) => {
    if (!orderStatus) return 'bg-gray-100 text-gray-800';
    
    switch (orderStatus.toLowerCase()) {
      case 'onaylandı':
        return 'bg-blue-100 text-blue-800';
      case 'hazırlanıyor':
        return 'bg-yellow-100 text-yellow-800';
      case 'servis için hazır':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={cn('backdrop-blur-sm border-0 shadow-md hover:shadow-xl transition-all duration-300', className)}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2.5">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-md shadow-blue-200/50">
                <span className="text-xs font-bold text-white">
                  {order.tableNumber}
                </span>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-slate-900">
                  Masa {order.tableNumber}
                </h4>
                <p className="text-xs text-slate-500 font-medium">
                  Sipariş #{order.id.slice(-6)}
                </p>
              </div>
            </div>

            {/* Status Badge for In Progress orders */}
            {status === 'in_progress' && order.status && (
              <span className={cn(
                'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold shadow-sm',
                getStatusColor(order.status)
              )}>
                {order.status}
              </span>
            )}
          </div>

          {/* Order Details */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <div className="flex items-center text-slate-600">
                <div className="w-6 h-6 bg-slate-100 rounded-md flex items-center justify-center mr-1.5">
                  <User className="w-3.5 h-3.5 text-slate-500" />
                </div>
                <span className="font-medium text-xs">{order.customerName}</span>
              </div>
              <div className="flex items-center text-slate-600">
                <div className="w-6 h-6 bg-slate-100 rounded-md flex items-center justify-center mr-1.5">
                  <Package className="w-3.5 h-3.5 text-slate-500" />
                </div>
                <span className="font-medium text-xs">{order.items} ürün</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-3 border-t border-slate-100">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewOrder?.(order.id)}
              className="text-xs font-semibold border-slate-200 text-slate-600 hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 h-8"
            >
              Detayları Gör
            </Button>

            {status === 'waiting_for_payment' && (
              <Button
                variant="primary"
                size="sm"
                onClick={() => onPayNow?.(order.id)}
                className="text-xs font-semibold bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 shadow-md shadow-emerald-200/50 transition-all duration-200 h-8"
              >
                <CreditCard className="w-3 h-3 mr-1" />
                Şimdi Öde
              </Button>
            )}

            {status === 'in_progress' && order.status === 'Servis için hazır' && (
              <Button
                variant="secondary"
                size="sm"
                className="text-xs font-semibold bg-gradient-to-r from-amber-500 to-orange-600 text-white hover:from-amber-600 hover:to-orange-700 shadow-md shadow-amber-200/50 transition-all duration-200 h-8"
              >
                <Clock className="w-3 h-3 mr-1" />
                Hazır
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
