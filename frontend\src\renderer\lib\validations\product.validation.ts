import { z } from 'zod';
import { ProductUnit } from '../../types/product.types';

// ==================== PRODUCT VALIDATION SCHEMAS ====================

export const productFormSchema = z.object({
  name: z.string()
    .min(1, 'Ürün adı zorunludur')
    .max(100, 'Ürün adı en fazla 100 karakter olabilir'),
  
  description: z.string()
    .max(500, 'Açıklama en fazla 500 karakter olabilir')
    .optional()
    .or(z.literal('')),
  
  basePrice: z.string()
    .min(1, 'Fiyat zorunludur')
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, 'Geçerli bir fiyat giriniz'),
  
  categoryId: z.string()
    .min(1, '<PERSON><PERSON><PERSON> seçimi zorunludur'),
  
  taxId: z.string()
    .min(1, 'Vergi oranı seçimi zorunludur'),
  
  code: z.string()
    .min(1, '<PERSON>rün kodu zorunludur')
    .max(50, 'Ürün kodu en fazla 50 karakter olabilir'),
  
  barcode: z.string()
    .max(50, 'Barkod en fazla 50 karakter olabilir')
    .optional()
    .or(z.literal('')),
  
  image: z.string()
    .url('Geçerli bir URL giriniz')
    .optional()
    .or(z.literal('')),
  
  unit: z.nativeEnum(ProductUnit),
  
  trackStock: z.boolean(),
  
  criticalStock: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0;
    }, 'Geçerli bir stok miktarı giriniz')
    .or(z.literal('')),
  
  available: z.boolean(),
  
  sellable: z.boolean(),
  
  preparationTime: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0 && num <= 1440;
    }, 'Hazırlık süresi 0-1440 dakika arasında olmalıdır')
    .or(z.literal('')),
  
  displayOrder: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0;
    }, 'Geçerli bir sıra numarası giriniz')
    .or(z.literal(''))
});

// ==================== CATEGORY VALIDATION SCHEMAS ====================

export const categoryFormSchema = z.object({
  name: z.string()
    .min(1, 'Kategori adı zorunludur')
    .max(100, 'Kategori adı en fazla 100 karakter olabilir'),
  
  description: z.string()
    .max(500, 'Açıklama en fazla 500 karakter olabilir')
    .optional()
    .or(z.literal('')),
  
  parentId: z.string()
    .optional()
    .or(z.literal('')),
  
  image: z.string()
    .url('Geçerli bir URL giriniz')
    .optional()
    .or(z.literal('')),
  
  color: z.string()
    .regex(/^#[0-9A-F]{6}$/i, 'Geçerli bir hex renk kodu giriniz (#RRGGBB)')
    .optional()
    .or(z.literal('')),
  
  icon: z.string()
    .max(50, 'Icon adı en fazla 50 karakter olabilir')
    .optional()
    .or(z.literal('')),
  
  displayOrder: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0;
    }, 'Geçerli bir sıra numarası giriniz')
    .or(z.literal('')),
  
  printerGroupId: z.string()
    .optional()
    .or(z.literal('')),
  
  preparationTime: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0 && num <= 1440;
    }, 'Hazırlık süresi 0-1440 dakika arasında olmalıdır')
    .or(z.literal(''))
});

// ==================== MODIFIER GROUP VALIDATION SCHEMAS ====================

export const modifierGroupFormSchema = z.object({
  name: z.string()
    .min(1, 'Modifiyer grup adı zorunludur')
    .max(100, 'Modifiyer grup adı en fazla 100 karakter olabilir'),
  
  minSelection: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0;
    }, 'Geçerli bir minimum seçim sayısı giriniz')
    .or(z.literal('')),
  
  maxSelection: z.string()
    .min(1, 'Maksimum seçim sayısı zorunludur')
    .refine((val) => {
      const num = parseInt(val);
      return !isNaN(num) && num >= 1;
    }, 'Maksimum seçim en az 1 olmalıdır'),
  
  required: z.boolean(),
  
  displayOrder: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0;
    }, 'Geçerli bir sıra numarası giriniz')
    .or(z.literal(''))
}).refine((data) => {
  const minSelection = parseInt(data.minSelection || '0');
  const maxSelection = parseInt(data.maxSelection);
  return minSelection <= maxSelection;
}, {
  message: 'Minimum seçim, maksimum seçimden büyük olamaz',
  path: ['minSelection']
});

// ==================== MODIFIER VALIDATION SCHEMAS ====================

export const modifierFormSchema = z.object({
  groupId: z.string()
    .min(1, 'Modifiyer grubu seçimi zorunludur'),
  
  name: z.string()
    .min(1, 'Modifiyer adı zorunludur')
    .max(100, 'Modifiyer adı en fazla 100 karakter olabilir'),
  
  price: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0;
    }, 'Geçerli bir fiyat giriniz')
    .or(z.literal('')),
  
  displayOrder: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === '') return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0;
    }, 'Geçerli bir sıra numarası giriniz')
    .or(z.literal(''))
});

// ==================== SEARCH VALIDATION SCHEMAS ====================

export const searchFormSchema = z.object({
  query: z.string()
    .max(100, 'Arama terimi en fazla 100 karakter olabilir')
    .optional()
    .or(z.literal(''))
});

// ==================== TYPE EXPORTS ====================

export type ProductFormData = z.infer<typeof productFormSchema>;
export type CategoryFormData = z.infer<typeof categoryFormSchema>;
export type ModifierGroupFormData = z.infer<typeof modifierGroupFormSchema>;
export type ModifierFormData = z.infer<typeof modifierFormSchema>;
export type SearchFormData = z.infer<typeof searchFormSchema>;

// ==================== HELPER FUNCTIONS ====================

export const convertFormDataToCreateProduct = (data: ProductFormData) => ({
  name: data.name,
  description: data.description || undefined,
  basePrice: parseFloat(data.basePrice),
  categoryId: data.categoryId,
  taxId: data.taxId,
  code: data.code,
  barcode: data.barcode || undefined,
  image: data.image || undefined,
  unit: data.unit,
  trackStock: data.trackStock,
  criticalStock: data.criticalStock ? parseFloat(data.criticalStock) : undefined,
  available: data.available,
  sellable: data.sellable,
  preparationTime: data.preparationTime ? parseInt(data.preparationTime) : undefined,
  displayOrder: data.displayOrder ? parseInt(data.displayOrder) : undefined
});

export const convertFormDataToCreateCategory = (data: CategoryFormData) => ({
  name: data.name,
  description: data.description || undefined,
  parentId: data.parentId || undefined,
  image: data.image || undefined,
  color: data.color || undefined,
  icon: data.icon || undefined,
  displayOrder: data.displayOrder ? parseInt(data.displayOrder) : undefined,
  printerGroupId: data.printerGroupId || undefined,
  preparationTime: data.preparationTime ? parseInt(data.preparationTime) : undefined
});

export const convertFormDataToCreateModifierGroup = (data: ModifierGroupFormData) => ({
  name: data.name,
  minSelection: data.minSelection ? parseInt(data.minSelection) : undefined,
  maxSelection: parseInt(data.maxSelection),
  required: data.required,
  displayOrder: data.displayOrder ? parseInt(data.displayOrder) : undefined
});

export const convertFormDataToCreateModifier = (data: ModifierFormData) => ({
  groupId: data.groupId,
  name: data.name,
  price: data.price ? parseFloat(data.price) : undefined,
  displayOrder: data.displayOrder ? parseInt(data.displayOrder) : undefined
});
