import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { ProductController } from '../controllers/product.controller.js';
import { authenticateToken, requireRole } from '../middlewares/auth.middleware.js';
import { validate } from '../middlewares/validation.middleware.js';
import {
  createProductSchema,
  updateProductSchema,
  getProductSchema,
  getProductsSchema,
  deleteProductSchema
} from '../validators/product.validator.js';

export const createProductRoutes = (prisma: PrismaClient): Router => {
  const router = Router();
  const productController = new ProductController(prisma);

  // Tüm product endpoint'leri authentication gerektirir
  router.use(authenticateToken);

  // Ürün yönetimi için gerekli roller
  const productManagementRoles = ['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'];
  
  // <PERSON>rün görüntüleme için gerekli roller (daha geni<PERSON> er<PERSON>im)
  const productViewRoles = ['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER', 'CASHIER', 'WAITER'];

  // ==================== PRODUCT ENDPOINTS ====================

  // GET /api/products - Tüm ürünleri listele (filtreleme, sayfalama ile)
  router.get(
    '/',
    requireRole(productViewRoles),
    validate(getProductsSchema),
    productController.getProducts
  );

  // GET /api/products/:id - Belirli bir ürünü getir
  router.get(
    '/:id',
    requireRole(productViewRoles),
    validate(getProductSchema),
    productController.getProductById
  );

  // POST /api/products - Yeni ürün ekle
  router.post(
    '/',
    requireRole(productManagementRoles),
    validate(createProductSchema),
    productController.createProduct
  );

  // PUT /api/products/:id - Mevcut ürünü güncelle
  router.put(
    '/:id',
    requireRole(productManagementRoles),
    validate(updateProductSchema),
    productController.updateProduct
  );

  // DELETE /api/products/:id - Ürünü sil
  router.delete(
    '/:id',
    requireRole(productManagementRoles),
    validate(deleteProductSchema),
    productController.deleteProduct
  );

  return router;
};
