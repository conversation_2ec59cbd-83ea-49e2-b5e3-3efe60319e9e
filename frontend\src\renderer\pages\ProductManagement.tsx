import React from 'react';
import {
  Plus,
  Package,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  FolderPlus
} from 'lucide-react';
import { Button } from '../components/common/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/common/Card';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { ProductList } from '../components/features/ProductList';
import { ProductForm } from '../components/features/ProductForm';
import { CategoryForm } from '../components/features/CategoryForm';
import { ProductSearch, ProductFilters } from '../components/features/ProductSearch';
import { DeleteConfirmationDialog } from '../components/features/DeleteConfirmationDialog';
import { useProductStore } from '../store/productStore';
import { cn } from '../lib/utils';

interface ProductManagementProps {
  className?: string;
}

export const ProductManagement: React.FC<ProductManagementProps> = ({ className }) => {
  const {
    productPagination,
    loading,
    error,
    isProductModalOpen,
    isDeleteDialogOpen,
    setProductPagination,
    openProductModal,
    closeProductModal,
    closeDeleteDialog,
    clearError
  } = useProductStore();

  const handlePageChange = (page: number) => {
    setProductPagination({ page });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleRefresh = () => {
    // This will trigger a refetch through React Query
    clearError();
    window.location.reload();
  };

  const renderPagination = () => {
    if (productPagination.totalPages <= 1) return null;

    const { page, totalPages } = productPagination;
    const pages = [];
    
    // Calculate page range to show
    const maxVisiblePages = 5;
    let startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex items-center justify-center gap-2 mt-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 1}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>

        {startPage > 1 && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(1)}
              className="h-8 w-8 p-0"
            >
              1
            </Button>
            {startPage > 2 && (
              <span className="text-gray-400 text-sm">...</span>
            )}
          </>
        )}

        {pages.map((pageNum) => (
          <Button
            key={pageNum}
            variant={pageNum === page ? "default" : "outline"}
            size="sm"
            onClick={() => handlePageChange(pageNum)}
            className="h-8 w-8 p-0"
          >
            {pageNum}
          </Button>
        ))}

        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && (
              <span className="text-gray-400 text-sm">...</span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(totalPages)}
              className="h-8 w-8 p-0"
            >
              {totalPages}
            </Button>
          </>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page + 1)}
          disabled={page === totalPages}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    );
  };

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Ürün Yönetimi
                </h1>
                <p className="text-sm text-gray-500">
                  Ürünleri görüntüleyin, ekleyin ve düzenleyin
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading.products}
                className="h-9"
              >
                <RotateCcw className={cn(
                  "w-4 h-4 mr-2",
                  loading.products && "animate-spin"
                )} />
                Yenile
              </Button>

              <Button
                variant="outline"
                onClick={() => openCategoryModal('create')}
                className="h-9"
              >
                <FolderPlus className="w-4 h-4 mr-2" />
                Yeni Kategori
              </Button>

              <Button
                onClick={() => openProductModal('create')}
                className="h-9"
              >
                <Plus className="w-4 h-4 mr-2" />
                Yeni Ürün
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Filters */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader className="pb-4">
                <CardTitle className="text-base">Filtreler</CardTitle>
              </CardHeader>
              <CardContent>
                <ProductFilters />
              </CardContent>
            </Card>
          </div>

          {/* Main Content - Products */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              {/* Search Bar */}
              <Card>
                <CardContent className="p-4">
                  <ProductSearch />
                </CardContent>
              </Card>

              {/* Results Summary */}
              {productPagination.total > 0 && (
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>
                    {productPagination.total} ürün bulundu
                  </span>
                  <span>
                    Sayfa {productPagination.page} / {productPagination.totalPages}
                  </span>
                </div>
              )}

              {/* Error Display */}
              {error && (
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 text-red-800">
                      <Package className="w-4 h-4" />
                      <span className="text-sm font-medium">Hata:</span>
                      <span className="text-sm">{error.message}</span>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Product List */}
              <ProductList />

              {/* Pagination */}
              {renderPagination()}
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <ProductForm
        open={isProductModalOpen}
        onClose={closeProductModal}
      />

      <CategoryForm
        open={isCategoryModalOpen}
        onClose={closeCategoryModal}
      />

      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
      />
    </div>
  );
};
