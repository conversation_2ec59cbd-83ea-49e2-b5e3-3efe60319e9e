import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { CategoryController } from '../controllers/category.controller.js';
import { authenticateToken, requireRole } from '../middlewares/auth.middleware.js';
import { validate } from '../middlewares/validation.middleware.js';
import {
  createCategorySchema,
  updateCategorySchema,
  getCategorySchema,
  getCategoriesSchema,
  deleteCategorySchema
} from '../validators/category.validator.js';

export const createCategoryRoutes = (prisma: PrismaClient): Router => {
  const router = Router();
  const categoryController = new CategoryController(prisma);

  // Tüm category endpoint'leri authentication gerektirir
  router.use(authenticateToken);

  // Kategori yönetimi için gerekli roller
  const categoryManagementRoles = ['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'];
  
  // <PERSON><PERSON>i gö<PERSON>üntü<PERSON>e için gerekli roller (daha geni<PERSON> er<PERSON>im)
  const categoryViewRoles = ['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER', 'CASHIER', 'WAITER'];

  // ==================== CATEGORY ENDPOINTS ====================

  // GET /api/categories - Tüm kategorileri listele
  router.get(
    '/',
    requireRole(categoryViewRoles),
    validate(getCategoriesSchema),
    categoryController.getCategories
  );

  // GET /api/categories/:id - Belirli bir kategoriyi getir
  router.get(
    '/:id',
    requireRole(categoryViewRoles),
    validate(getCategorySchema),
    categoryController.getCategoryById
  );

  // POST /api/categories - Yeni kategori ekle
  router.post(
    '/',
    requireRole(categoryManagementRoles),
    validate(createCategorySchema),
    categoryController.createCategory
  );

  // PUT /api/categories/:id - Mevcut kategoriyi güncelle
  router.put(
    '/:id',
    requireRole(categoryManagementRoles),
    validate(updateCategorySchema),
    categoryController.updateCategory
  );

  // DELETE /api/categories/:id - Kategoriyi sil
  router.delete(
    '/:id',
    requireRole(categoryManagementRoles),
    validate(deleteCategorySchema),
    categoryController.deleteCategory
  );

  return router;
};
