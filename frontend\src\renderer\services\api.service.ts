import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { AvailableShiftUser, LoginRequest, LoginResponse } from '../types/auth.types';
import {
  DashboardSummary,
  PopularDish,
  OutOfStockItem,
  DashboardOrder,
  DashboardResponse,
  OrdersQueryParams
} from '../types/dashboard.types';
import {
  Product,
  Category,
  ModifierGroup,
  Modifier,
  CreateProductRequest,
  UpdateProductRequest,
  GetProductsQuery,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  GetCategoriesQuery,
  CreateModifierGroupRequest,
  UpdateModifierGroupRequest,
  CreateModifierRequest,
  UpdateModifierRequest,
  GetModifierGroupsQuery,
  GetModifiersQuery,
  ApiResponse,
  PaginatedResponse
} from '../types/product.types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          // Redirect to login if needed
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async getAvailableShifts(branchId?: string): Promise<AvailableShiftUser[]> {
    const params = branchId ? { branchId } : {};
    const response = await this.api.get('/users/available-shifts', { params });
    return response.data;
  }

  async pinLogin(request: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post('/auth/pin-login', request);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.api.get('/health');
    return response.data;
  }

  // Dashboard endpoints
  async getDashboardSummary(): Promise<DashboardResponse<DashboardSummary>> {
    const response = await this.api.get('/dashboard/summary');
    return response.data;
  }

  async getPopularDishes(limit?: number): Promise<DashboardResponse<PopularDish[]>> {
    const params = limit ? { limit } : {};
    const response = await this.api.get('/dashboard/popular-dishes', { params });
    return response.data;
  }

  async getOutOfStockItems(): Promise<DashboardResponse<OutOfStockItem[]>> {
    const response = await this.api.get('/dashboard/out-of-stock');
    return response.data;
  }

  async getDashboardOrders(params: OrdersQueryParams): Promise<DashboardResponse<DashboardOrder[]>> {
    const response = await this.api.get('/dashboard/orders', { params });
    return response.data;
  }

  async getDashboardHealth(): Promise<any> {
    const response = await this.api.get('/dashboard/health');
    return response.data;
  }

  // ==================== PRODUCT ENDPOINTS ====================

  async getProducts(params?: GetProductsQuery): Promise<PaginatedResponse<Product>> {
    const response = await this.api.get('/products', { params });
    return response.data;
  }

  async getProductById(id: string): Promise<ApiResponse<Product>> {
    const response = await this.api.get(`/products/${id}`);
    return response.data;
  }

  async createProduct(data: CreateProductRequest): Promise<ApiResponse<Product>> {
    const response = await this.api.post('/products', data);
    return response.data;
  }

  async updateProduct(id: string, data: UpdateProductRequest): Promise<ApiResponse<Product>> {
    const response = await this.api.put(`/products/${id}`, data);
    return response.data;
  }

  async deleteProduct(id: string): Promise<ApiResponse<string>> {
    const response = await this.api.delete(`/products/${id}`);
    return response.data;
  }

  // ==================== CATEGORY ENDPOINTS ====================

  async getCategories(params?: GetCategoriesQuery): Promise<PaginatedResponse<Category>> {
    const response = await this.api.get('/categories', { params });
    return response.data;
  }

  async getCategoryById(id: string): Promise<ApiResponse<Category>> {
    const response = await this.api.get(`/categories/${id}`);
    return response.data;
  }

  async createCategory(data: CreateCategoryRequest): Promise<ApiResponse<Category>> {
    const response = await this.api.post('/categories', data);
    return response.data;
  }

  async updateCategory(id: string, data: UpdateCategoryRequest): Promise<ApiResponse<Category>> {
    const response = await this.api.put(`/categories/${id}`, data);
    return response.data;
  }

  async deleteCategory(id: string): Promise<ApiResponse<string>> {
    const response = await this.api.delete(`/categories/${id}`);
    return response.data;
  }

  // ==================== MODIFIER GROUP ENDPOINTS ====================

  async getModifierGroups(params?: GetModifierGroupsQuery): Promise<PaginatedResponse<ModifierGroup>> {
    const response = await this.api.get('/modifiers/groups', { params });
    return response.data;
  }

  async getModifierGroupById(id: string): Promise<ApiResponse<ModifierGroup>> {
    const response = await this.api.get(`/modifiers/groups/${id}`);
    return response.data;
  }

  async createModifierGroup(data: CreateModifierGroupRequest): Promise<ApiResponse<ModifierGroup>> {
    const response = await this.api.post('/modifiers/groups', data);
    return response.data;
  }

  async updateModifierGroup(id: string, data: UpdateModifierGroupRequest): Promise<ApiResponse<ModifierGroup>> {
    const response = await this.api.put(`/modifiers/groups/${id}`, data);
    return response.data;
  }

  async deleteModifierGroup(id: string): Promise<ApiResponse<string>> {
    const response = await this.api.delete(`/modifiers/groups/${id}`);
    return response.data;
  }

  // ==================== MODIFIER ENDPOINTS ====================

  async getModifiers(params?: GetModifiersQuery): Promise<PaginatedResponse<Modifier>> {
    const response = await this.api.get('/modifiers', { params });
    return response.data;
  }

  async getModifierById(id: string): Promise<ApiResponse<Modifier>> {
    const response = await this.api.get(`/modifiers/${id}`);
    return response.data;
  }

  async createModifier(data: CreateModifierRequest): Promise<ApiResponse<Modifier>> {
    const response = await this.api.post('/modifiers', data);
    return response.data;
  }

  async updateModifier(id: string, data: UpdateModifierRequest): Promise<ApiResponse<Modifier>> {
    const response = await this.api.put(`/modifiers/${id}`, data);
    return response.data;
  }

  async deleteModifier(id: string): Promise<ApiResponse<string>> {
    const response = await this.api.delete(`/modifiers/${id}`);
    return response.data;
  }

  // ==================== TAX ENDPOINTS (TEMPORARY) ====================

  async getTaxes(): Promise<ApiResponse<any[]>> {
    // Temporary mock data until tax endpoints are implemented
    return Promise.resolve({
      success: true,
      data: [
        { id: 'tax1', name: 'KDV %18', rate: 18, active: true },
        { id: 'tax2', name: 'KDV %8', rate: 8, active: true },
        { id: 'tax3', name: 'KDV %1', rate: 1, active: true }
      ]
    });
  }
}

export const apiService = new ApiService();
