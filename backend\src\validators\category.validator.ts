import { z } from 'zod';

// ==================== CATEGORY SCHEMAS ====================

export const createCategorySchema = z.object({
  body: z.object({
    name: z.string()
      .min(1, 'Kategori adı zorunludur')
      .max(100, 'Kategori adı en fazla 100 karakter olabilir'),
    
    description: z.string()
      .max(500, 'Açıklama en fazla 500 karakter olabilir')
      .optional(),
    
    parentId: z.string()
      .optional(),
    
    image: z.string()
      .url('Geçerli bir URL giriniz')
      .optional(),
    
    color: z.string()
      .regex(/^#[0-9A-F]{6}$/i, 'Geçerli bir hex renk kodu giriniz (#RRGGBB)')
      .optional(),
    
    icon: z.string()
      .max(50, 'Icon adı en fazla 50 karakter olabilir')
      .optional(),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .default(0),
    
    printerGroupId: z.string()
      .optional(),
    
    preparationTime: z.number()
      .min(0, 'Hazırlık süresi negatif olamaz')
      .max(1440, 'Hazırlık süresi en fazla 24 saat olabilir')
      .optional()
  })
});

export const updateCategorySchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Kategori ID zorunludur')
  }),
  body: z.object({
    name: z.string()
      .min(1, 'Kategori adı zorunludur')
      .max(100, 'Kategori adı en fazla 100 karakter olabilir')
      .optional(),
    
    description: z.string()
      .max(500, 'Açıklama en fazla 500 karakter olabilir')
      .optional(),
    
    parentId: z.string()
      .optional(),
    
    image: z.string()
      .url('Geçerli bir URL giriniz')
      .optional(),
    
    color: z.string()
      .regex(/^#[0-9A-F]{6}$/i, 'Geçerli bir hex renk kodu giriniz (#RRGGBB)')
      .optional(),
    
    icon: z.string()
      .max(50, 'Icon adı en fazla 50 karakter olabilir')
      .optional(),
    
    displayOrder: z.number()
      .min(0, 'Görüntüleme sırası negatif olamaz')
      .optional(),
    
    active: z.boolean()
      .optional(),
    
    printerGroupId: z.string()
      .optional(),
    
    preparationTime: z.number()
      .min(0, 'Hazırlık süresi negatif olamaz')
      .max(1440, 'Hazırlık süresi en fazla 24 saat olabilir')
      .optional()
  })
});

export const getCategorySchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Kategori ID zorunludur')
  })
});

export const getCategoriesSchema = z.object({
  query: z.object({
    page: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0, 'Sayfa numarası pozitif olmalıdır')
      .default('1'),
    
    limit: z.string()
      .transform(val => parseInt(val))
      .refine(val => val > 0 && val <= 100, 'Limit 1-100 arasında olmalıdır')
      .default('20'),
    
    parentId: z.string()
      .optional(),
    
    search: z.string()
      .max(100, 'Arama terimi en fazla 100 karakter olabilir')
      .optional(),
    
    active: z.string()
      .transform(val => val === 'true')
      .optional(),
    
    includeProducts: z.string()
      .transform(val => val === 'true')
      .default('false')
  })
});

export const deleteCategorySchema = z.object({
  params: z.object({
    id: z.string().min(1, 'Kategori ID zorunludur')
  })
});

// ==================== TYPE EXPORTS ====================

export type CreateCategoryInput = z.infer<typeof createCategorySchema>['body'];
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>['body'];
export type GetCategoriesQuery = z.infer<typeof getCategoriesSchema>['query'];
