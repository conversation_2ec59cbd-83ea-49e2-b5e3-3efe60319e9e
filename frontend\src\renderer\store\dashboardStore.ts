// dashboardStore.ts
// Dashboard için Zustand store

import { create } from 'zustand';
import { 
  DashboardSummary, 
  PopularDish, 
  OutOfStockItem, 
  DashboardOrder, 
  DashboardFilters,
  DashboardLoadingState,
  DashboardError,
  OrderStatus
} from '../types/dashboard.types';

interface DashboardState {
  // Data
  summary: DashboardSummary | null;
  popularDishes: PopularDish[];
  outOfStockItems: OutOfStockItem[];
  orders: DashboardOrder[];
  
  // UI State
  filters: DashboardFilters;
  loading: DashboardLoadingState;
  error: DashboardError | null;
  
  // Actions
  setSummary: (summary: DashboardSummary) => void;
  setPopularDishes: (dishes: PopularDish[]) => void;
  setOutOfStockItems: (items: OutOfStockItem[]) => void;
  setOrders: (orders: DashboardOrder[]) => void;
  setFilters: (filters: Partial<DashboardFilters>) => void;
  setLoading: (key: keyof DashboardLoadingState, value: boolean) => void;
  setError: (error: DashboardError | null) => void;
  clearError: () => void;
  reset: () => void;
}

const initialFilters: DashboardFilters = {
  orderStatus: 'in_progress',
  searchQuery: ''
};

const initialLoading: DashboardLoadingState = {
  summary: false,
  popularDishes: false,
  outOfStock: false,
  orders: false
};

export const useDashboardStore = create<DashboardState>((set, get) => ({
  // Initial state
  summary: null,
  popularDishes: [],
  outOfStockItems: [],
  orders: [],
  filters: initialFilters,
  loading: initialLoading,
  error: null,

  // Actions
  setSummary: (summary) => set({ summary }),
  
  setPopularDishes: (popularDishes) => set({ popularDishes }),
  
  setOutOfStockItems: (outOfStockItems) => set({ outOfStockItems }),
  
  setOrders: (orders) => set({ orders }),
  
  setFilters: (newFilters) => set((state) => ({
    filters: { ...state.filters, ...newFilters }
  })),
  
  setLoading: (key, value) => set((state) => ({
    loading: { ...state.loading, [key]: value }
  })),
  
  setError: (error) => set({ error }),
  
  clearError: () => set({ error: null }),
  
  reset: () => set({
    summary: null,
    popularDishes: [],
    outOfStockItems: [],
    orders: [],
    filters: initialFilters,
    loading: initialLoading,
    error: null
  })
}));
