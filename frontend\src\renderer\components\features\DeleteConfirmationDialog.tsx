import React from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as AlertDialog from '@radix-ui/react-alert-dialog';
import * as Toast from '@radix-ui/react-toast';
import { 
  AlertTriangle, 
  Trash2, 
  X, 
  Loader2 
} from 'lucide-react';
import { Button } from '../common/Button';
import { useProductStore } from '../../store/productStore';
import { apiService } from '../../services/api.service';
import { cn } from '../../lib/utils';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
}

export const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({ 
  open, 
  onClose 
}) => {
  const queryClient = useQueryClient();
  const {
    deleteTarget,
    removeProduct,
    removeCategory,
    removeModifierGroup,
    removeModifier,
    setError
  } = useProductStore();

  // Delete mutations for different types
  const deleteProductMutation = useMutation({
    mutationFn: (id: string) => apiService.deleteProduct(id),
    onSuccess: (response, id) => {
      if (response.success) {
        removeProduct(id);
        queryClient.invalidateQueries({ queryKey: ['products'] });
        onClose();
        // Show success toast
        showSuccessToast('Ürün başarıyla silindi');
      }
    },
    onError: (error) => {
      setError({
        message: error instanceof Error ? error.message : 'Ürün silinirken hata oluştu',
        type: 'delete'
      });
    }
  });

  const deleteCategoryMutation = useMutation({
    mutationFn: (id: string) => apiService.deleteCategory(id),
    onSuccess: (response, id) => {
      if (response.success) {
        removeCategory(id);
        queryClient.invalidateQueries({ queryKey: ['categories'] });
        onClose();
        showSuccessToast('Kategori başarıyla silindi');
      }
    },
    onError: (error) => {
      setError({
        message: error instanceof Error ? error.message : 'Kategori silinirken hata oluştu',
        type: 'delete'
      });
    }
  });

  const deleteModifierGroupMutation = useMutation({
    mutationFn: (id: string) => apiService.deleteModifierGroup(id),
    onSuccess: (response, id) => {
      if (response.success) {
        removeModifierGroup(id);
        queryClient.invalidateQueries({ queryKey: ['modifierGroups'] });
        onClose();
        showSuccessToast('Modifiyer grubu başarıyla silindi');
      }
    },
    onError: (error) => {
      setError({
        message: error instanceof Error ? error.message : 'Modifiyer grubu silinirken hata oluştu',
        type: 'delete'
      });
    }
  });

  const deleteModifierMutation = useMutation({
    mutationFn: (id: string) => apiService.deleteModifier(id),
    onSuccess: (response, id) => {
      if (response.success) {
        removeModifier(id);
        queryClient.invalidateQueries({ queryKey: ['modifiers'] });
        onClose();
        showSuccessToast('Modifiyer başarıyla silindi');
      }
    },
    onError: (error) => {
      setError({
        message: error instanceof Error ? error.message : 'Modifiyer silinirken hata oluştu',
        type: 'delete'
      });
    }
  });

  const showSuccessToast = (message: string) => {
    // This would be implemented with a proper toast system
    console.log('Success:', message);
  };

  const handleDelete = () => {
    if (!deleteTarget) return;

    switch (deleteTarget.type) {
      case 'product':
        deleteProductMutation.mutate(deleteTarget.id);
        break;
      case 'category':
        deleteCategoryMutation.mutate(deleteTarget.id);
        break;
      case 'modifierGroup':
        deleteModifierGroupMutation.mutate(deleteTarget.id);
        break;
      case 'modifier':
        deleteModifierMutation.mutate(deleteTarget.id);
        break;
    }
  };

  const isLoading = 
    deleteProductMutation.isPending ||
    deleteCategoryMutation.isPending ||
    deleteModifierGroupMutation.isPending ||
    deleteModifierMutation.isPending;

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'product':
        return 'ürünü';
      case 'category':
        return 'kategoriyi';
      case 'modifierGroup':
        return 'modifiyer grubunu';
      case 'modifier':
        return 'modifiyeri';
      default:
        return 'öğeyi';
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'product':
        return <Trash2 className="w-6 h-6 text-red-500" />;
      case 'category':
        return <Trash2 className="w-6 h-6 text-red-500" />;
      case 'modifierGroup':
        return <Trash2 className="w-6 h-6 text-red-500" />;
      case 'modifier':
        return <Trash2 className="w-6 h-6 text-red-500" />;
      default:
        return <AlertTriangle className="w-6 h-6 text-red-500" />;
    }
  };

  const getWarningMessage = (type: string) => {
    switch (type) {
      case 'product':
        return 'Bu ürün silindiğinde, aktif siparişlerde kullanılıyorsa işlem başarısız olacaktır.';
      case 'category':
        return 'Bu kategori silindiğinde, içindeki ürünler ve alt kategoriler de etkilenebilir.';
      case 'modifierGroup':
        return 'Bu modifiyer grubu silindiğinde, içindeki tüm modifiyerler de silinecektir.';
      case 'modifier':
        return 'Bu modifiyer silindiğinde, aktif siparişlerde kullanılıyorsa işlem başarısız olacaktır.';
      default:
        return 'Bu işlem geri alınamaz.';
    }
  };

  if (!deleteTarget) return null;

  return (
    <AlertDialog.Root open={open} onOpenChange={onClose}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <AlertDialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-md">
          <div className="p-6">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                {getIcon(deleteTarget.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <AlertDialog.Title className="text-lg font-semibold text-gray-900 mb-2">
                  {getTypeLabel(deleteTarget.type).charAt(0).toUpperCase() + getTypeLabel(deleteTarget.type).slice(1)} Sil
                </AlertDialog.Title>
                
                <AlertDialog.Description className="text-sm text-gray-600 mb-4">
                  <span className="font-medium">"{deleteTarget.name}"</span> adlı {getTypeLabel(deleteTarget.type)} silmek istediğinizden emin misiniz?
                </AlertDialog.Description>

                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                    <p className="text-xs text-yellow-800">
                      {getWarningMessage(deleteTarget.type)}
                    </p>
                  </div>
                </div>

                <p className="text-xs text-gray-500 mb-6">
                  Bu işlem geri alınamaz. Silinen veriler kalıcı olarak kaldırılacaktır.
                </p>
              </div>
            </div>

            <div className="flex items-center justify-end gap-3">
              <AlertDialog.Cancel asChild>
                <Button
                  variant="outline"
                  disabled={isLoading}
                  className="min-w-[80px]"
                >
                  İptal
                </Button>
              </AlertDialog.Cancel>
              
              <AlertDialog.Action asChild>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isLoading}
                  className="min-w-[80px]"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Sil
                    </>
                  )}
                </Button>
              </AlertDialog.Action>
            </div>
          </div>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  );
};
