// MoreModal.tsx
// "Daha <PERSON>azla" modal component'i

import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import {
  X,
  Store,
  BarChart3,
  Users,
  Package,
  Wifi,
  Bell,
  Eye,
  Globe,
  HelpCircle,
  Shield,
  Info,
  LogOut
} from 'lucide-react';
import { Button } from '../common/Button';

export interface MoreModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNavigate?: (page: string) => void;
}

interface MoreMenuItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  category: 'tools' | 'settings' | 'help';
  onClick?: () => void;
}

export const MoreModal: React.FC<MoreModalProps> = ({ isOpen, onClose, onNavigate }) => {
  const menuItems: MoreMenuItem[] = [
    // Tools
    {
      id: 'outlet-info',
      title: 'Şube Bilgileri',
      icon: <Store className="w-5 h-5" />,
      category: 'tools',
      onClick: () => console.log('Outlet Information')
    },
    {
      id: 'reports',
      title: '<PERSON><PERSON>',
      icon: <BarChart3 className="w-5 h-5" />,
      category: 'tools',
      onClick: () => console.log('Report Summary')
    },
    {
      id: 'customers',
      title: 'Müşteri Verileri',
      icon: <Users className="w-5 h-5" />,
      category: 'tools',
      onClick: () => console.log('Customer Data')
    },
    {
      id: 'product-management',
      title: 'Ürün Yönetimi',
      icon: <Package className="w-5 h-5" />,
      category: 'tools',
      onClick: () => {
        onNavigate?.('product-management');
        onClose();
      }
    },
    
    // Settings
    {
      id: 'devices',
      title: 'Bağlı Cihazlar',
      icon: <Wifi className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Connected Devices')
    },
    {
      id: 'notifications',
      title: 'Bildirimler',
      icon: <Bell className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Notifications')
    },
    {
      id: 'appearance',
      title: 'Görünüm',
      icon: <Eye className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Appearance')
    },
    {
      id: 'language',
      title: 'Dil',
      icon: <Globe className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Language')
    },
    
    // Help
    {
      id: 'help-center',
      title: 'Yardım Merkezi',
      icon: <HelpCircle className="w-5 h-5" />,
      category: 'help',
      onClick: () => console.log('Help Center')
    },
    {
      id: 'privacy',
      title: 'Gizlilik Politikası',
      icon: <Shield className="w-5 h-5" />,
      category: 'help',
      onClick: () => console.log('Privacy Policy')
    },
    {
      id: 'app-info',
      title: 'Uygulama Bilgileri',
      icon: <Info className="w-5 h-5" />,
      category: 'help',
      onClick: () => console.log('App Information')
    }
  ];

  const categories = [
    { id: 'tools', title: 'Araçlar', items: menuItems.filter(item => item.category === 'tools') },
    { id: 'settings', title: 'Ayarlar', items: menuItems.filter(item => item.category === 'settings') },
    { id: 'help', title: 'Yardım', items: menuItems.filter(item => item.category === 'help') }
  ];

  const handleItemClick = (item: MoreMenuItem) => {
    item.onClick?.();
    onClose();
  };

  const handleLogout = () => {
    // TODO: Implement logout functionality
    console.log('Logout');
    onClose();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed left-1/2 top-1/2 z-50 w-full max-w-4xl -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl shadow-2xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] max-h-[85vh] overflow-hidden border border-slate-200/50">
          
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-blue-50/30">
            <Dialog.Title className="text-xl font-bold text-slate-800">
              Daha Fazla
            </Dialog.Title>
            <Dialog.Close asChild>
              <Button variant="ghost" size="icon" className="h-10 w-10 hover:bg-slate-100 rounded-full">
                <X className="w-5 h-5 text-slate-600" />
              </Button>
            </Dialog.Close>
          </div>

          {/* Content */}
          <div className="overflow-y-auto max-h-[calc(85vh-140px)]">
            <div className="p-6">
              {/* Horizontal Layout */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {categories.map((category) => (
                  <div key={category.id} className="space-y-4">
                    <h3 className="text-sm font-bold text-slate-600 uppercase tracking-wider border-b border-slate-200 pb-2">
                      {category.title}
                    </h3>
                    <div className="space-y-3">
                      {category.items.map((item) => (
                        <button
                          key={item.id}
                          onClick={() => handleItemClick(item)}
                          className="w-full flex items-center p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200 group border border-transparent hover:border-blue-200/50 hover:shadow-md"
                        >
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center mr-4 group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-200 shadow-sm">
                            <div className="text-blue-600 group-hover:text-blue-700 transition-colors duration-200">
                              {item.icon}
                            </div>
                          </div>
                          <span className="text-sm font-semibold text-slate-700 group-hover:text-slate-800 transition-colors duration-200">
                            {item.title}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-slate-200/60 p-6 bg-gradient-to-r from-slate-50 to-blue-50/30">
            <Button
              variant="outline"
              onClick={handleLogout}
              className="w-full flex items-center justify-center space-x-3 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 py-3 rounded-xl font-semibold transition-all duration-200 hover:shadow-md"
            >
              <LogOut className="w-5 h-5" />
              <span>Çıkış Yap</span>
            </Button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
