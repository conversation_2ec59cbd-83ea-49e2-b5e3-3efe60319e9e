import { create } from 'zustand';
import { 
  Product, 
  Category, 
  ModifierGroup, 
  Modifier,
  GetProductsQuery,
  GetCategoriesQuery,
  GetModifierGroupsQuery,
  GetModifiersQuery
} from '../types/product.types';

// ==================== INTERFACES ====================

interface ProductFilters {
  search: string;
  categoryId: string;
  available?: boolean;
  sellable?: boolean;
  trackStock?: boolean;
}

interface CategoryFilters {
  search: string;
  parentId?: string;
  active?: boolean;
  includeProducts: boolean;
}

interface ModifierGroupFilters {
  search: string;
  active?: boolean;
  includeModifiers: boolean;
}

interface ModifierFilters {
  search: string;
  groupId?: string;
  active?: boolean;
}

interface ProductLoadingState {
  products: boolean;
  categories: boolean;
  modifierGroups: boolean;
  modifiers: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
}

interface ProductPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface ProductError {
  message: string;
  type: 'products' | 'categories' | 'modifierGroups' | 'modifiers' | 'create' | 'update' | 'delete';
}

// ==================== STORE INTERFACE ====================

interface ProductState {
  // Data
  products: Product[];
  categories: Category[];
  modifierGroups: ModifierGroup[];
  modifiers: Modifier[];
  selectedProduct: Product | null;
  selectedCategory: Category | null;
  selectedModifierGroup: ModifierGroup | null;
  selectedModifier: Modifier | null;
  
  // Filters
  productFilters: ProductFilters;
  categoryFilters: CategoryFilters;
  modifierGroupFilters: ModifierGroupFilters;
  modifierFilters: ModifierFilters;
  
  // Pagination
  productPagination: ProductPagination;
  categoryPagination: ProductPagination;
  modifierGroupPagination: ProductPagination;
  modifierPagination: ProductPagination;
  
  // Loading states
  loading: ProductLoadingState;
  
  // Error state
  error: ProductError | null;
  
  // Modal states
  isProductModalOpen: boolean;
  isCategoryModalOpen: boolean;
  isModifierGroupModalOpen: boolean;
  isModifierModalOpen: boolean;
  isDeleteDialogOpen: boolean;
  deleteTarget: { type: 'product' | 'category' | 'modifierGroup' | 'modifier'; id: string; name: string } | null;
  
  // Form modes
  productFormMode: 'create' | 'edit';
  categoryFormMode: 'create' | 'edit';
  modifierGroupFormMode: 'create' | 'edit';
  modifierFormMode: 'create' | 'edit';
  
  // Actions - Data
  setProducts: (products: Product[]) => void;
  setCategories: (categories: Category[]) => void;
  setModifierGroups: (modifierGroups: ModifierGroup[]) => void;
  setModifiers: (modifiers: Modifier[]) => void;
  setSelectedProduct: (product: Product | null) => void;
  setSelectedCategory: (category: Category | null) => void;
  setSelectedModifierGroup: (modifierGroup: ModifierGroup | null) => void;
  setSelectedModifier: (modifier: Modifier | null) => void;
  
  // Actions - Filters
  setProductFilters: (filters: Partial<ProductFilters>) => void;
  setCategoryFilters: (filters: Partial<CategoryFilters>) => void;
  setModifierGroupFilters: (filters: Partial<ModifierGroupFilters>) => void;
  setModifierFilters: (filters: Partial<ModifierFilters>) => void;
  clearFilters: () => void;
  
  // Actions - Pagination
  setProductPagination: (pagination: Partial<ProductPagination>) => void;
  setCategoryPagination: (pagination: Partial<ProductPagination>) => void;
  setModifierGroupPagination: (pagination: Partial<ProductPagination>) => void;
  setModifierPagination: (pagination: Partial<ProductPagination>) => void;
  
  // Actions - Loading
  setLoading: (key: keyof ProductLoadingState, value: boolean) => void;
  
  // Actions - Error
  setError: (error: ProductError | null) => void;
  clearError: () => void;
  
  // Actions - Modals
  openProductModal: (mode: 'create' | 'edit', product?: Product) => void;
  closeProductModal: () => void;
  openCategoryModal: (mode: 'create' | 'edit', category?: Category) => void;
  closeCategoryModal: () => void;
  openModifierGroupModal: (mode: 'create' | 'edit', modifierGroup?: ModifierGroup) => void;
  closeModifierGroupModal: () => void;
  openModifierModal: (mode: 'create' | 'edit', modifier?: Modifier) => void;
  closeModifierModal: () => void;
  openDeleteDialog: (type: 'product' | 'category' | 'modifierGroup' | 'modifier', id: string, name: string) => void;
  closeDeleteDialog: () => void;
  
  // Actions - Utility
  addProduct: (product: Product) => void;
  updateProduct: (product: Product) => void;
  removeProduct: (id: string) => void;
  addCategory: (category: Category) => void;
  updateCategory: (category: Category) => void;
  removeCategory: (id: string) => void;
  addModifierGroup: (modifierGroup: ModifierGroup) => void;
  updateModifierGroup: (modifierGroup: ModifierGroup) => void;
  removeModifierGroup: (id: string) => void;
  addModifier: (modifier: Modifier) => void;
  updateModifier: (modifier: Modifier) => void;
  removeModifier: (id: string) => void;
  
  // Actions - Query builders
  getProductsQuery: () => GetProductsQuery;
  getCategoriesQuery: () => GetCategoriesQuery;
  getModifierGroupsQuery: () => GetModifierGroupsQuery;
  getModifiersQuery: () => GetModifiersQuery;
}

// ==================== INITIAL STATES ====================

const initialProductFilters: ProductFilters = {
  search: '',
  categoryId: '',
  available: undefined,
  sellable: undefined,
  trackStock: undefined
};

const initialCategoryFilters: CategoryFilters = {
  search: '',
  parentId: undefined,
  active: undefined,
  includeProducts: false
};

const initialModifierGroupFilters: ModifierGroupFilters = {
  search: '',
  active: undefined,
  includeModifiers: false
};

const initialModifierFilters: ModifierFilters = {
  search: '',
  groupId: undefined,
  active: undefined
};

const initialPagination: ProductPagination = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
};

const initialLoading: ProductLoadingState = {
  products: false,
  categories: false,
  modifierGroups: false,
  modifiers: false,
  creating: false,
  updating: false,
  deleting: false
};

// ==================== STORE IMPLEMENTATION ====================

export const useProductStore = create<ProductState>((set, get) => ({
  // Initial state
  products: [],
  categories: [],
  modifierGroups: [],
  modifiers: [],
  selectedProduct: null,
  selectedCategory: null,
  selectedModifierGroup: null,
  selectedModifier: null,
  
  productFilters: initialProductFilters,
  categoryFilters: initialCategoryFilters,
  modifierGroupFilters: initialModifierGroupFilters,
  modifierFilters: initialModifierFilters,
  
  productPagination: initialPagination,
  categoryPagination: initialPagination,
  modifierGroupPagination: initialPagination,
  modifierPagination: initialPagination,
  
  loading: initialLoading,
  error: null,
  
  isProductModalOpen: false,
  isCategoryModalOpen: false,
  isModifierGroupModalOpen: false,
  isModifierModalOpen: false,
  isDeleteDialogOpen: false,
  deleteTarget: null,
  
  productFormMode: 'create',
  categoryFormMode: 'create',
  modifierGroupFormMode: 'create',
  modifierFormMode: 'create',
  
  // Data actions
  setProducts: (products) => set({ products }),
  setCategories: (categories) => set({ categories }),
  setModifierGroups: (modifierGroups) => set({ modifierGroups }),
  setModifiers: (modifiers) => set({ modifiers }),
  setSelectedProduct: (selectedProduct) => set({ selectedProduct }),
  setSelectedCategory: (selectedCategory) => set({ selectedCategory }),
  setSelectedModifierGroup: (selectedModifierGroup) => set({ selectedModifierGroup }),
  setSelectedModifier: (selectedModifier) => set({ selectedModifier }),
  
  // Filter actions
  setProductFilters: (filters) => set((state) => ({
    productFilters: { ...state.productFilters, ...filters }
  })),
  setCategoryFilters: (filters) => set((state) => ({
    categoryFilters: { ...state.categoryFilters, ...filters }
  })),
  setModifierGroupFilters: (filters) => set((state) => ({
    modifierGroupFilters: { ...state.modifierGroupFilters, ...filters }
  })),
  setModifierFilters: (filters) => set((state) => ({
    modifierFilters: { ...state.modifierFilters, ...filters }
  })),
  clearFilters: () => set({
    productFilters: initialProductFilters,
    categoryFilters: initialCategoryFilters,
    modifierGroupFilters: initialModifierGroupFilters,
    modifierFilters: initialModifierFilters
  }),
  
  // Pagination actions
  setProductPagination: (pagination) => set((state) => ({
    productPagination: { ...state.productPagination, ...pagination }
  })),
  setCategoryPagination: (pagination) => set((state) => ({
    categoryPagination: { ...state.categoryPagination, ...pagination }
  })),
  setModifierGroupPagination: (pagination) => set((state) => ({
    modifierGroupPagination: { ...state.modifierGroupPagination, ...pagination }
  })),
  setModifierPagination: (pagination) => set((state) => ({
    modifierPagination: { ...state.modifierPagination, ...pagination }
  })),
  
  // Loading actions
  setLoading: (key, value) => set((state) => ({
    loading: { ...state.loading, [key]: value }
  })),
  
  // Error actions
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
  
  // Modal actions
  openProductModal: (mode, product) => set({
    isProductModalOpen: true,
    productFormMode: mode,
    selectedProduct: product || null
  }),
  closeProductModal: () => set({
    isProductModalOpen: false,
    selectedProduct: null
  }),
  openCategoryModal: (mode, category) => set({
    isCategoryModalOpen: true,
    categoryFormMode: mode,
    selectedCategory: category || null
  }),
  closeCategoryModal: () => set({
    isCategoryModalOpen: false,
    selectedCategory: null
  }),
  openModifierGroupModal: (mode, modifierGroup) => set({
    isModifierGroupModalOpen: true,
    modifierGroupFormMode: mode,
    selectedModifierGroup: modifierGroup || null
  }),
  closeModifierGroupModal: () => set({
    isModifierGroupModalOpen: false,
    selectedModifierGroup: null
  }),
  openModifierModal: (mode, modifier) => set({
    isModifierModalOpen: true,
    modifierFormMode: mode,
    selectedModifier: modifier || null
  }),
  closeModifierModal: () => set({
    isModifierModalOpen: false,
    selectedModifier: null
  }),
  openDeleteDialog: (type, id, name) => set({
    isDeleteDialogOpen: true,
    deleteTarget: { type, id, name }
  }),
  closeDeleteDialog: () => set({
    isDeleteDialogOpen: false,
    deleteTarget: null
  }),
  
  // Utility actions
  addProduct: (product) => set((state) => ({
    products: [...state.products, product]
  })),
  updateProduct: (product) => set((state) => ({
    products: state.products.map(p => p.id === product.id ? product : p),
    selectedProduct: state.selectedProduct?.id === product.id ? product : state.selectedProduct
  })),
  removeProduct: (id) => set((state) => ({
    products: state.products.filter(p => p.id !== id),
    selectedProduct: state.selectedProduct?.id === id ? null : state.selectedProduct
  })),
  addCategory: (category) => set((state) => ({
    categories: [...state.categories, category]
  })),
  updateCategory: (category) => set((state) => ({
    categories: state.categories.map(c => c.id === category.id ? category : c),
    selectedCategory: state.selectedCategory?.id === category.id ? category : state.selectedCategory
  })),
  removeCategory: (id) => set((state) => ({
    categories: state.categories.filter(c => c.id !== id),
    selectedCategory: state.selectedCategory?.id === id ? null : state.selectedCategory
  })),
  addModifierGroup: (modifierGroup) => set((state) => ({
    modifierGroups: [...state.modifierGroups, modifierGroup]
  })),
  updateModifierGroup: (modifierGroup) => set((state) => ({
    modifierGroups: state.modifierGroups.map(mg => mg.id === modifierGroup.id ? modifierGroup : mg),
    selectedModifierGroup: state.selectedModifierGroup?.id === modifierGroup.id ? modifierGroup : state.selectedModifierGroup
  })),
  removeModifierGroup: (id) => set((state) => ({
    modifierGroups: state.modifierGroups.filter(mg => mg.id !== id),
    selectedModifierGroup: state.selectedModifierGroup?.id === id ? null : state.selectedModifierGroup
  })),
  addModifier: (modifier) => set((state) => ({
    modifiers: [...state.modifiers, modifier]
  })),
  updateModifier: (modifier) => set((state) => ({
    modifiers: state.modifiers.map(m => m.id === modifier.id ? modifier : m),
    selectedModifier: state.selectedModifier?.id === modifier.id ? modifier : state.selectedModifier
  })),
  removeModifier: (id) => set((state) => ({
    modifiers: state.modifiers.filter(m => m.id !== id),
    selectedModifier: state.selectedModifier?.id === id ? null : state.selectedModifier
  })),
  
  // Query builders
  getProductsQuery: () => {
    const { productFilters, productPagination } = get();
    const query: GetProductsQuery = {
      page: productPagination.page,
      limit: productPagination.limit
    };
    
    if (productFilters.search) query.search = productFilters.search;
    if (productFilters.categoryId) query.categoryId = productFilters.categoryId;
    if (productFilters.available !== undefined) query.available = productFilters.available;
    if (productFilters.sellable !== undefined) query.sellable = productFilters.sellable;
    if (productFilters.trackStock !== undefined) query.trackStock = productFilters.trackStock;
    
    return query;
  },
  
  getCategoriesQuery: () => {
    const { categoryFilters, categoryPagination } = get();
    const query: GetCategoriesQuery = {
      page: categoryPagination.page,
      limit: categoryPagination.limit,
      includeProducts: categoryFilters.includeProducts
    };
    
    if (categoryFilters.search) query.search = categoryFilters.search;
    if (categoryFilters.parentId !== undefined) query.parentId = categoryFilters.parentId;
    if (categoryFilters.active !== undefined) query.active = categoryFilters.active;
    
    return query;
  },
  
  getModifierGroupsQuery: () => {
    const { modifierGroupFilters, modifierGroupPagination } = get();
    const query: GetModifierGroupsQuery = {
      page: modifierGroupPagination.page,
      limit: modifierGroupPagination.limit,
      includeModifiers: modifierGroupFilters.includeModifiers
    };
    
    if (modifierGroupFilters.search) query.search = modifierGroupFilters.search;
    if (modifierGroupFilters.active !== undefined) query.active = modifierGroupFilters.active;
    
    return query;
  },
  
  getModifiersQuery: () => {
    const { modifierFilters, modifierPagination } = get();
    const query: GetModifiersQuery = {
      page: modifierPagination.page,
      limit: modifierPagination.limit
    };
    
    if (modifierFilters.search) query.search = modifierFilters.search;
    if (modifierFilters.groupId) query.groupId = modifierFilters.groupId;
    if (modifierFilters.active !== undefined) query.active = modifierFilters.active;
    
    return query;
  }
}));
