import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Edit, 
  Trash2, 
  Package, 
  Eye, 
  EyeOff,
  ShoppingCart,
  AlertTriangle,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../common/Card';
import { Button } from '../common/Button';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { EmptyState } from '../common/EmptyState';
import { useProductStore } from '../../store/productStore';
import { apiService } from '../../services/api.service';
import { Product } from '../../types/product.types';
import { cn } from '../../lib/utils';

interface ProductListProps {
  className?: string;
}

interface ProductCardProps {
  product: Product;
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onEdit, onDelete }) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(price);
  };

  const getStatusColor = (product: Product) => {
    if (!product.active) return 'bg-gray-100 text-gray-600';
    if (!product.available) return 'bg-red-100 text-red-600';
    if (!product.sellable) return 'bg-yellow-100 text-yellow-600';
    return 'bg-green-100 text-green-600';
  };

  const getStatusText = (product: Product) => {
    if (!product.active) return 'Pasif';
    if (!product.available) return 'Mevcut Değil';
    if (!product.sellable) return 'Satışta Değil';
    return 'Aktif';
  };

  const getStatusIcon = (product: Product) => {
    if (!product.active) return <EyeOff className="w-3 h-3" />;
    if (!product.available) return <AlertTriangle className="w-3 h-3" />;
    if (!product.sellable) return <Eye className="w-3 h-3" />;
    return <ShoppingCart className="w-3 h-3" />;
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 border-gray-200/50 hover:border-gray-300">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-sm font-semibold text-gray-900 truncate">
              {product.name}
            </CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-gray-500 font-mono">
                {product.code}
              </span>
              {product.barcode && (
                <span className="text-xs text-gray-400 font-mono">
                  | {product.barcode}
                </span>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-1 ml-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(product)}
              className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Edit className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(product)}
              className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Product Image */}
        {product.image ? (
          <div className="w-full h-32 bg-gray-100 rounded-lg mb-3 overflow-hidden">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <div className="hidden w-full h-full flex items-center justify-center bg-gray-100">
              <Package className="w-8 h-8 text-gray-400" />
            </div>
          </div>
        ) : (
          <div className="w-full h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
            <Package className="w-8 h-8 text-gray-400" />
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between mb-3">
          <span className="text-lg font-bold text-gray-900">
            {formatPrice(product.basePrice)}
          </span>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {product.unit}
          </span>
        </div>

        {/* Category */}
        {product.category && (
          <div className="flex items-center gap-2 mb-3">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: product.category.color || '#6B7280' }}
            />
            <span className="text-xs text-gray-600 truncate">
              {product.category.name}
            </span>
          </div>
        )}

        {/* Description */}
        {product.description && (
          <p className="text-xs text-gray-500 mb-3 line-clamp-2">
            {product.description}
          </p>
        )}

        {/* Status and Features */}
        <div className="space-y-2">
          <div className={cn(
            "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
            getStatusColor(product)
          )}>
            {getStatusIcon(product)}
            {getStatusText(product)}
          </div>

          <div className="flex items-center gap-2 text-xs text-gray-500">
            {product.trackStock && (
              <div className="flex items-center gap-1">
                <Package className="w-3 h-3" />
                <span>Stok Takipli</span>
              </div>
            )}
            {product.preparationTime && (
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                <span>{product.preparationTime}dk</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const ProductList: React.FC<ProductListProps> = ({ className }) => {
  const {
    products,
    loading,
    error,
    getProductsQuery,
    setProducts,
    setProductPagination,
    setLoading,
    setError,
    openProductModal,
    openDeleteDialog
  } = useProductStore();

  const query = getProductsQuery();

  const { data, isLoading, isError, error: queryError } = useQuery({
    queryKey: ['products', query],
    queryFn: () => apiService.getProducts(query),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  React.useEffect(() => {
    if (data?.success) {
      setProducts(data.data);
      if (data.pagination) {
        setProductPagination(data.pagination);
      }
    }
  }, [data, setProducts, setProductPagination]);

  React.useEffect(() => {
    setLoading('products', isLoading);
  }, [isLoading, setLoading]);

  React.useEffect(() => {
    if (isError && queryError) {
      setError({
        message: queryError instanceof Error ? queryError.message : 'Ürünler yüklenirken hata oluştu',
        type: 'products'
      });
    } else {
      setError(null);
    }
  }, [isError, queryError, setError]);

  const handleEdit = (product: Product) => {
    openProductModal('edit', product);
  };

  const handleDelete = (product: Product) => {
    openDeleteDialog('product', product.id, product.name);
  };

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className={cn("py-12", className)}>
        <EmptyState
          icon={<AlertTriangle className="w-12 h-12 text-red-500" />}
          title="Hata Oluştu"
          description={error?.message || 'Ürünler yüklenirken bir hata oluştu'}
        />
      </div>
    );
  }

  if (!products.length) {
    return (
      <div className={cn("py-12", className)}>
        <EmptyState
          icon={<Package className="w-12 h-12 text-gray-400" />}
          title="Ürün Bulunamadı"
          description="Henüz hiç ürün eklenmemiş veya arama kriterlerinize uygun ürün bulunamadı."
        />
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        ))}
      </div>
    </div>
  );
};
